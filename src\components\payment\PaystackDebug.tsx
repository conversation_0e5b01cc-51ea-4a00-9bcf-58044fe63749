import React, { useState } from 'react';
import { View, StyleSheet, Alert, ScrollView } from 'react-native';
import { Button, Text, Card, Divider } from 'react-native-paper';
import { Paystack } from 'react-native-paystack-webview';
import { PaystackAdvanced } from './PaystackAdvanced';
import { PAYSTACK_CONFIG } from '../../constants';
import { toCents } from '../../utils/currency';

export const PaystackDebug = () => {
  const [showOriginal, setShowOriginal] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [testAmount] = useState(100);

  const handleSuccess = (response: any, type: string) => {
    console.log(`✅ ${type} Payment Success:`, response);
    Alert.alert(
      `${type} Success!`, 
      `Payment completed!\n\nReference: ${response.reference}\nAmount: KES ${testAmount}`,
      [{ text: 'OK' }]
    );
    setShowOriginal(false);
    setShowAdvanced(false);
  };

  const handleCancel = (type: string) => {
    console.log(`❌ ${type} Payment Cancelled`);
    setShowOriginal(false);
    setShowAdvanced(false);
  };

  const testOriginalPaystack = () => {
    console.log('🧪 Testing Original Paystack Component...');
    console.log('🔑 Public Key:', PAYSTACK_CONFIG.publicKey);
    console.log('💰 Amount:', testAmount, 'KES =', toCents(testAmount), 'cents');
    setShowOriginal(true);
  };

  const testAdvancedPaystack = () => {
    console.log('🧪 Testing Advanced Paystack Component...');
    setShowAdvanced(true);
  };

  const checkPaystackConfig = () => {
    const config = {
      publicKey: PAYSTACK_CONFIG.publicKey,
      currency: PAYSTACK_CONFIG.currency,
      isTestKey: PAYSTACK_CONFIG.publicKey.startsWith('pk_test_'),
      keyLength: PAYSTACK_CONFIG.publicKey.length,
    };
    
    console.log('🔍 Paystack Configuration:', config);
    
    Alert.alert(
      'Paystack Configuration',
      `Public Key: ${config.publicKey.substring(0, 20)}...\n` +
      `Currency: ${config.currency}\n` +
      `Test Mode: ${config.isTestKey ? 'Yes' : 'No'}\n` +
      `Key Length: ${config.keyLength} chars`,
      [{ text: 'OK' }]
    );
  };

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.title}>🔧 Paystack Debug Center</Text>
          <Text style={styles.subtitle}>Test different Paystack configurations</Text>
          
          <Divider style={styles.divider} />
          
          <Text style={styles.sectionTitle}>Configuration Check</Text>
          <Button
            mode="outlined"
            onPress={checkPaystackConfig}
            style={styles.button}
            icon="cog"
          >
            Check Paystack Config
          </Button>
          
          <Divider style={styles.divider} />
          
          <Text style={styles.sectionTitle}>Test Original Component</Text>
          <Text style={styles.description}>
            This uses the standard react-native-paystack-webview component
          </Text>
          <Button
            mode="contained"
            onPress={testOriginalPaystack}
            style={[styles.button, styles.primaryButton]}
            icon="credit-card"
          >
            Test Original Paystack
          </Button>
          
          <Divider style={styles.divider} />
          
          <Text style={styles.sectionTitle}>Test Advanced Component</Text>
          <Text style={styles.description}>
            This uses a custom WebView with forced payment methods
          </Text>
          <Button
            mode="contained"
            onPress={testAdvancedPaystack}
            style={[styles.button, styles.secondaryButton]}
            icon="cellphone"
          >
            Test Advanced Paystack
          </Button>
          
          <Divider style={styles.divider} />
          
          <Text style={styles.info}>
            💡 If you only see "Pay with Card" and "Pay with Transfer", 
            your Paystack account might need to be configured for Kenya 
            and M-Pesa specifically.
          </Text>
        </Card.Content>
      </Card>

      {/* Original Paystack Component */}
      {showOriginal && (
        <Paystack
          paystackKey={PAYSTACK_CONFIG.publicKey}
          amount={toCents(testAmount)}
          billingEmail="<EMAIL>"
          billingName="Debug User"
          currency="KES"
          reference={`DEBUG_ORIG_${Date.now()}`}
          onCancel={() => handleCancel('Original')}
          onSuccess={(response) => handleSuccess(response, 'Original')}
          autoStart={false}
          billingMobile="************"
          channels={['mobile_money', 'card', 'bank', 'ussd', 'qr', 'bank_transfer']}
          metadata={{
            debug_mode: true,
            test_type: 'original_component'
          }}
        />
      )}

      {/* Advanced Paystack Component */}
      <PaystackAdvanced
        visible={showAdvanced}
        publicKey={PAYSTACK_CONFIG.publicKey}
        email="<EMAIL>"
        amount={toCents(testAmount)}
        currency="KES"
        reference={`DEBUG_ADV_${Date.now()}`}
        onSuccess={(response) => handleSuccess(response, 'Advanced')}
        onCancel={() => handleCancel('Advanced')}
        customerName="Debug User"
        customerPhone="************"
        metadata={{
          debug_mode: true,
          test_type: 'advanced_component'
        }}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  card: {
    padding: 20,
    elevation: 4,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#1976D2',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
    lineHeight: 20,
  },
  button: {
    marginBottom: 16,
  },
  primaryButton: {
    backgroundColor: '#1976D2',
  },
  secondaryButton: {
    backgroundColor: '#2E7D32',
  },
  divider: {
    marginVertical: 20,
  },
  info: {
    fontSize: 12,
    color: '#FF9800',
    fontStyle: 'italic',
    textAlign: 'center',
    lineHeight: 18,
  },
});
