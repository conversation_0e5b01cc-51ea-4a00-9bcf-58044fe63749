-- Script to fix room schema issues and test room creation
-- Run this in your Supabase SQL editor

-- First, run the migration to add missing fields
\i supabase/migrations/20241203_add_missing_room_fields.sql

-- Then, ensure sample rooms are added with the new schema
\i supabase/ensure-sample-rooms.sql

-- Test room creation with the new schema
INSERT INTO public.rooms (
    room_number, 
    room_type, 
    bed_type,
    size_sqm,
    price_per_night, 
    description, 
    amenities, 
    max_occupancy, 
    status,
    is_available,
    images
) VALUES (
    'TEST001', 
    'standard', 
    'Queen Bed',
    25.0,
    8500.00, 
    'Test room to verify schema fixes',
    ARRAY['WiFi', 'Air Conditioning', 'TV'], 
    2, 
    'available',
    true,
    '[{"id": "test_img_1", "url": "https://images.unsplash.com/photo-1566665797739-1674de7a421a?w=800&q=80", "alt_text": "Test room", "upload_date": "2024-12-03T00:00:00Z", "file_name": "test_room.jpg"}]'::jsonb
);

-- Verify the test room was created
SELECT 
    room_number,
    room_type,
    bed_type,
    size_sqm,
    price_per_night,
    is_available,
    status,
    jsonb_array_length(images) as image_count
FROM public.rooms 
WHERE room_number = 'TEST001';

-- Clean up test room
DELETE FROM public.rooms WHERE room_number = 'TEST001';

-- Show final room count and status
SELECT 
    'Room Schema Fix Complete' as status,
    COUNT(*) as total_rooms,
    COUNT(*) FILTER (WHERE status = 'available' AND is_available = true) as available_rooms,
    COUNT(*) FILTER (WHERE status = 'maintenance') as maintenance_rooms,
    COUNT(*) FILTER (WHERE status = 'cleaning') as cleaning_rooms,
    COUNT(*) FILTER (WHERE status = 'booked' OR is_available = false) as occupied_rooms
FROM public.rooms;
