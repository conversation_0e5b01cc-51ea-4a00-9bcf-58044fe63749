import { create } from 'zustand';
import { notificationService, NotificationData } from '../services/notifications';
import { realtimeService, RealtimeSubscription } from '../services/realtime';
import { formatPrice } from '../utils/currency';
import type { Notification } from '@/types';

interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  settings: NotificationSettings;
  loading: false;
  error: string | null;
}

interface NotificationSettings {
  pushEnabled: boolean;
  emailEnabled: boolean;
  categories: {
    bookings: boolean;
    payments: boolean;
    reminders: boolean;
    promotions: boolean;
    maintenance: boolean;
  };
}

interface NotificationStore extends NotificationState {
  // Notification management
  addNotification: (notification: Notification) => void;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  removeNotification: (notificationId: string) => void;
  clearNotifications: () => void;
  
  // Settings
  updateSettings: (settings: Partial<NotificationSettings>) => void;
  toggleCategory: (category: keyof NotificationSettings['categories']) => void;
  
  // Real-time subscriptions
  subscriptions: Map<string, RealtimeSubscription>;
  subscribeToUpdates: (userId: string, userRole: string) => void;
  unsubscribeFromUpdates: () => void;
  
  // Push notifications
  initializePushNotifications: () => Promise<void>;
  sendNotification: (data: NotificationData) => Promise<void>;
  scheduleNotification: (data: NotificationData, date: Date) => Promise<string>;
  
  // Hotel-specific notifications
  handleReservationUpdate: (reservation: any) => void;
  handlePaymentUpdate: (payment: any) => void;
  handleRoomUpdate: (room: any) => void;
}

export const useNotificationStore = create((set: any, get: any) => ({
  // Initial state
  notifications: [],
  unreadCount: 0,
  settings: {
    pushEnabled: true,
    emailEnabled: true,
    categories: {
      bookings: true,
      payments: true,
      reminders: true,
      promotions: false,
      maintenance: true,
    },
  },
  loading: false,
  error: null,
  subscriptions: new Map(),

  // Notification management
  addNotification: (notification: Notification) => {
    set((state: any) => ({
      notifications: [notification, ...state.notifications],
      unreadCount: notification.read ? state.unreadCount : state.unreadCount + 1,
    }));
  },

  markAsRead: (notificationId: string) => {
    set((state: any) => ({
      notifications: state.notifications.map((notification: any) =>
        notification.id === notificationId
          ? { ...notification, read: true }
          : notification
      ),
      unreadCount: Math.max(0, state.unreadCount - 1),
    }));
  },

  markAllAsRead: () => {
    set((state: any) => ({
      notifications: state.notifications.map((notification: any) => ({
        ...notification,
        read: true,
      })),
      unreadCount: 0,
    }));
  },

  removeNotification: (notificationId: string) => {
    set((state: any) => {
      const notification = state.notifications.find((n: any) => n.id === notificationId);
      return {
        notifications: state.notifications.filter((n: any) => n.id !== notificationId),
        unreadCount: notification && !notification.read 
          ? Math.max(0, state.unreadCount - 1)
          : state.unreadCount,
      };
    });
  },

  clearNotifications: () => {
    set({
      notifications: [],
      unreadCount: 0,
    });
  },

  // Settings
  updateSettings: (newSettings: Partial<NotificationSettings>) => {
    set((state) => ({
      settings: {
        ...state.settings,
        ...newSettings,
      },
    }));
  },

  toggleCategory: (category: keyof NotificationSettings['categories']) => {
    set((state) => ({
      settings: {
        ...state.settings,
        categories: {
          ...state.settings.categories,
          [category]: !state.settings.categories[category],
        },
      },
    }));
  },

  // Real-time subscriptions
  subscribeToUpdates: (userId: string, userRole: string) => {
    const { subscriptions } = get();
    
    // Clear existing subscriptions
    subscriptions.forEach((subscription) => subscription.unsubscribe());
    subscriptions.clear();

    // Subscribe to user-specific reservations for guests
    if (userRole === 'guest') {
      const reservationSub = realtimeService.subscribeToUserReservations(
        userId,
        (payload) => {
          get().handleReservationUpdate(payload);
        }
      );
      subscriptions.set('user-reservations', reservationSub);
    }

    // Subscribe to all updates for admin/staff
    if (userRole === 'admin' || userRole === 'receptionist') {
      const roomSub = realtimeService.subscribeToRooms((payload) => {
        get().handleRoomUpdate(payload);
      });
      subscriptions.set('rooms', roomSub);

      const reservationSub = realtimeService.subscribeToReservations((payload) => {
        get().handleReservationUpdate(payload);
      });
      subscriptions.set('reservations', reservationSub);

      const paymentSub = realtimeService.subscribeToPayments((payload) => {
        get().handlePaymentUpdate(payload);
      });
      subscriptions.set('payments', paymentSub);
    }

    set({ subscriptions });
  },

  unsubscribeFromUpdates: () => {
    const { subscriptions } = get();
    subscriptions.forEach((subscription) => subscription.unsubscribe());
    subscriptions.clear();
    set({ subscriptions });
  },

  // Push notifications
  initializePushNotifications: async () => {
    try {
      await notificationService.initialize();
      const pushToken = await notificationService.registerForPushNotifications();
      
      if (pushToken) {
        console.log('Push token registered:', pushToken.token);
        // Here you would typically save the token to your backend
      }

      // Set up notification listeners
      notificationService.addNotificationReceivedListener((notification) => {
        const newNotification: Notification = {
          id: Date.now().toString(),
          title: notification.request.content.title || 'Notification',
          message: notification.request.content.body || '',
          type: notification.request.content.data?.type || 'general',
          read: false,
          createdAt: new Date().toISOString(),
          data: notification.request.content.data,
        };
        
        get().addNotification(newNotification);
      });

      notificationService.addNotificationResponseReceivedListener((response) => {
        const { data } = response.notification.request.content;
        
        // Handle different notification actions
        switch (response.actionIdentifier) {
          case 'view':
          case 'view_details':
            // Navigate to relevant screen based on data
            console.log('Navigate to details:', data);
            break;
          case 'pay_now':
            // Navigate to payment screen
            console.log('Navigate to payment:', data);
            break;
          case 'cancel':
            // Handle cancellation
            console.log('Handle cancellation:', data);
            break;
          default:
            // Default action (tap notification)
            console.log('Default notification action:', data);
        }
      });
    } catch (error) {
      console.error('Failed to initialize push notifications:', error);
      set({ error: 'Failed to initialize notifications' });
    }
  },

  sendNotification: async (data: NotificationData) => {
    const { settings } = get();
    
    if (!settings.pushEnabled) return;
    
    try {
      await notificationService.sendLocalNotification(data);
    } catch (error) {
      console.error('Failed to send notification:', error);
    }
  },

  scheduleNotification: async (data: NotificationData, date: Date) => {
    const { settings } = get();
    
    if (!settings.pushEnabled) return '';
    
    try {
      return await notificationService.scheduleLocalNotification(data, { date });
    } catch (error) {
      console.error('Failed to schedule notification:', error);
      return '';
    }
  },

  // Hotel-specific notification handlers
  handleReservationUpdate: (payload: any) => {
    const { settings } = get();
    if (!settings.categories.bookings) return;

    const { eventType, new: newData, old: oldData } = payload;
    
    let notification: Notification | null = null;

    switch (eventType) {
      case 'INSERT':
        notification = {
          id: `reservation-${newData.id}`,
          title: 'New Booking Created',
          message: `Reservation for Room ${newData.room?.room_number || 'N/A'} has been created.`,
          type: 'booking',
          read: false,
          createdAt: new Date().toISOString(),
          data: newData,
        };
        break;
        
      case 'UPDATE':
        if (oldData?.status !== newData?.status) {
          const statusMessages = {
            confirmed: 'Your booking has been confirmed! 🎉',
            checked_in: 'Welcome! You have successfully checked in.',
            checked_out: 'Thank you for staying with us! Have a great day.',
            cancelled: 'Your booking has been cancelled.',
          };
          
          notification = {
            id: `reservation-status-${newData.id}`,
            title: 'Booking Status Updated',
            message: statusMessages[newData.status as keyof typeof statusMessages] || 
                    `Your booking status has been updated to ${newData.status}.`,
            type: 'booking',
            read: false,
            createdAt: new Date().toISOString(),
            data: newData,
          };
        }
        break;
    }

    if (notification) {
      get().addNotification(notification);
    }
  },

  handlePaymentUpdate: (payload: any) => {
    const { settings } = get();
    if (!settings.categories.payments) return;

    const { eventType, new: newData, old: oldData } = payload;
    
    let notification: Notification | null = null;

    switch (eventType) {
      case 'INSERT':
        notification = {
          id: `payment-${newData.id}`,
          title: 'Payment Initiated',
          message: `Payment of ${formatPrice(newData.amount || 0)} has been initiated.`,
          type: 'payment',
          read: false,
          createdAt: new Date().toISOString(),
          data: newData,
        };
        break;
        
      case 'UPDATE':
        if (oldData?.status !== newData?.status) {
          const statusMessages = {
            paid: `Payment successful! Amount: ${formatPrice(newData.amount || 0)}`,
            failed: 'Payment failed. Please try again or contact support.',
            refunded: `Refund processed: ${formatPrice(newData.amount || 0)}`,
          };
          
          notification = {
            id: `payment-status-${newData.id}`,
            title: 'Payment Status Updated',
            message: statusMessages[newData.status as keyof typeof statusMessages] || 
                    `Payment status updated to ${newData.status}.`,
            type: 'payment',
            read: false,
            createdAt: new Date().toISOString(),
            data: newData,
          };
        }
        break;
    }

    if (notification) {
      get().addNotification(notification);
    }
  },

  handleRoomUpdate: (payload: any) => {
    const { settings } = get();
    if (!settings.categories.maintenance) return;

    const { eventType, new: newData, old: oldData } = payload;
    
    if (eventType === 'UPDATE' && oldData?.status !== newData?.status) {
      const statusMessages = {
        maintenance: `Room ${newData.room_number} requires maintenance attention.`,
        cleaning: `Room ${newData.room_number} is being prepared for next guest.`,
        available: `Room ${newData.room_number} is now available for booking.`,
        booked: `Room ${newData.room_number} has been booked.`,
      };
      
      const notification: Notification = {
        id: `room-status-${newData.id}`,
        title: 'Room Status Updated',
        message: statusMessages[newData.status as keyof typeof statusMessages] || 
                `Room ${newData.room_number} status updated to ${newData.status}.`,
        type: 'maintenance',
        read: false,
        createdAt: new Date().toISOString(),
        data: newData,
      };

      get().addNotification(notification);
    }
  },
}));
