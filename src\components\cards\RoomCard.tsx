// filepath: c:\Users\<USER>\OneDrive - GTECHNOLOGY\Projects\sunset-view-hotel\src\components\cards\RoomCard.tsx
import React from 'react';
import ReactNative from 'react-native';
const {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} = ReactNative;
type ViewStyle = ReactNative.ViewProps["style"];
import { Ionicons } from '@expo/vector-icons';
import { COLORS, TYPOGRAPHY, SPACING } from '../../constants';
import { formatPrice } from '../../utils/currency';
import type { Room } from '../../types/database';

export interface RoomCardProps {
  room: Room;
  onPress: () => void;
  showStatus?: boolean;
  style?: ViewStyle;
}

const { width } = Dimensions.get('window');

export function RoomCard({ room, onPress, showStatus = false, style }: RoomCardProps) {
  const getStatusColor = (status: Room['status']) => {
    switch (status) {
      case 'available':
        return COLORS.success;
      case 'booked':
        return COLORS.error;
      case 'maintenance':
        return COLORS.warning;
      case 'cleaning':
        return COLORS.info;
      default:
        return COLORS.textSecondary;
    }
  };

  const getRoomTypeDisplay = (type: Room['room_type']) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  const defaultImage = 'https://images.unsplash.com/photo-1566665797739-1674de7a421a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80';

  // Get the first image URL, handling both legacy string arrays and new RoomImage objects
  const getFirstImageUrl = () => {
    if (!room.images || room.images.length === 0) {
      return defaultImage;
    }

    const firstImage = room.images[0];
    if (typeof firstImage === 'string') {
      // Legacy format
      return firstImage;
    }
    // New format - RoomImage object
    return firstImage.url;
  };

  const getFirstImageAltText = () => {
    if (!room.images || room.images.length === 0) {
      return `Room ${room.room_number}`;
    }

    const firstImage = room.images[0];
    if (typeof firstImage === 'string') {
      // Legacy format
      return `Room ${room.room_number}`;
    }
    // New format - RoomImage object
    return firstImage.alt_text || `Room ${room.room_number}`;
  };

  return (
    <TouchableOpacity style={[styles.container, style]} onPress={onPress} activeOpacity={0.8}>
      <View style={styles.imageContainer}>
        <Image
          source={{ uri: getFirstImageUrl() }}
          style={styles.image}
          defaultSource={{ uri: defaultImage }}
          accessibilityLabel={getFirstImageAltText()}
        />
        {showStatus && (
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(room.status) }]}>
            <Text style={styles.statusText}>{room.status.toUpperCase()}</Text>
          </View>
        )}
        <View style={styles.priceBadge}>
          <Text style={styles.priceText}>{formatPrice(room.price_per_night)}</Text>
          <Text style={styles.priceSubtext}>per night</Text>
        </View>
      </View>

      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.roomNumber}>Room {room.room_number}</Text>
          <Text style={styles.roomType}>{getRoomTypeDisplay(room.room_type)}</Text>
        </View>

        <Text style={styles.description} numberOfLines={2}>
          {room.description}
        </Text>

        <View style={styles.amenitiesContainer}>
          <View style={styles.amenityItem}>
            <Ionicons name="people-outline" size={16} color={COLORS.primary} />
            <Text style={styles.amenityText}>Up to {room.max_occupancy} guests</Text>
          </View>
          {room.amenities && room.amenities.slice(0, 2).map((amenity, index) => (
            <View key={index} style={styles.amenityItem}>
              <Ionicons name="checkmark-circle-outline" size={16} color={COLORS.success} />
              <Text style={styles.amenityText}>{amenity}</Text>
            </View>
          ))}
          {room.amenities && room.amenities.length > 2 && (
            <Text style={styles.moreAmenities}>
              +{room.amenities.length - 2} more
            </Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.background,
    borderRadius: 12,
    marginBottom: SPACING.md,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  imageContainer: {
    position: 'relative',
    height: 200,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  statusBadge: {
    position: 'absolute',
    top: SPACING.sm,
    left: SPACING.sm,
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs / 2,
    borderRadius: 4,
  },
  statusText: {
    ...TYPOGRAPHY.caption,
    color: '#fff',
    fontWeight: '600',
  },
  priceBadge: {
    position: 'absolute',
    top: SPACING.sm,
    right: SPACING.sm,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: 6,
    alignItems: 'center',
  },
  priceText: {
    ...TYPOGRAPHY.body2,
    color: '#fff',
    fontWeight: '700',
  },
  priceSubtext: {
    ...TYPOGRAPHY.caption,
    color: '#fff',
    opacity: 0.8,
  },
  content: {
    padding: SPACING.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  roomNumber: {
    ...TYPOGRAPHY.h4,
    color: COLORS.text,
  },
  roomType: {
    ...TYPOGRAPHY.body2,
    color: COLORS.primary,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  description: {
    ...TYPOGRAPHY.body2,
    color: COLORS.textSecondary,
    marginBottom: SPACING.md,
    lineHeight: 20,
  },
  amenitiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  amenityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: SPACING.md,
    marginBottom: SPACING.xs,
  },
  amenityText: {
    ...TYPOGRAPHY.caption,
    color: COLORS.textSecondary,
    marginLeft: SPACING.xs,
  },
  moreAmenities: {
    ...TYPOGRAPHY.caption,
    color: COLORS.primary,
    fontWeight: '600',
  },
});

export default RoomCard;