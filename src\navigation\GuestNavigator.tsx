import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../constants';
import type { GuestStackParamList } from '../types/navigation';

// Screen imports
import { HomeScreen } from '../screens/guest/HomeScreen';
import { RoomsScreen } from '../screens/guest/RoomsScreen';
import { RoomDetailsScreen } from '../screens/guest/RoomDetailsScreen';
import { BookingScreen } from '../screens/guest/BookingScreen';
import { PaymentScreen } from '../screens/guest/PaymentScreen';
import { ReservationsScreen } from '../screens/guest/ReservationsScreen';
import { ReservationDetailsScreen } from '../screens/guest/ReservationDetailsScreen';
import { ProfileScreen } from '../screens/guest/ProfileScreen';

const Tab = createBottomTabNavigator();
const Stack = createNativeStackNavigator();

// Export navigation prop type for use in screens
export type GuestNavigationProp = {
  navigate: (screen: keyof GuestStackParamList, params?: any) => void;
  goBack: () => void;
  push: (screen: keyof GuestStackParamList, params?: any) => void;
  pop: (count?: number) => void;
  popToTop: () => void;
  setParams: (params: any) => void;
  addListener: (event: string, callback: () => void) => () => void;
  dispatch: (action: any) => void;
};

// Home Stack
function HomeStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="HomeMain" 
        component={HomeScreen} 
        options={{ title: 'Sunset View Hotel' }}
      />
      <Stack.Screen 
        name="RoomDetails" 
        component={RoomDetailsScreen}
        options={{ title: 'Room Details' }}
      />
      <Stack.Screen 
        name="Booking" 
        component={BookingScreen}
        options={{ title: 'Book Room' }}
      />
      <Stack.Screen 
        name="Payment" 
        component={PaymentScreen}
        options={{ title: 'Payment' }}
      />
    </Stack.Navigator>
  );
}

// Rooms Stack
function RoomsStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="RoomsMain" 
        component={RoomsScreen} 
        options={{ title: 'Available Rooms' }}
      />
      <Stack.Screen 
        name="RoomDetails" 
        component={RoomDetailsScreen}
        options={{ title: 'Room Details' }}
      />
      <Stack.Screen 
        name="Booking" 
        component={BookingScreen}
        options={{ title: 'Book Room' }}
      />
      <Stack.Screen 
        name="Payment" 
        component={PaymentScreen}
        options={{ title: 'Payment' }}
      />
    </Stack.Navigator>
  );
}

// Reservations Stack
function ReservationsStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="ReservationsMain" 
        component={ReservationsScreen} 
        options={{ title: 'My Reservations' }}
      />
      <Stack.Screen
        name="ReservationDetails"
        component={ReservationDetailsScreen}
        options={{ title: 'Reservation Details' }}
      />
    </Stack.Navigator>
  );
}

// Profile Stack
function ProfileStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="ProfileMain" 
        component={ProfileScreen} 
        options={{ title: 'Profile' }}
      />
    </Stack.Navigator>
  );
}

export default function GuestNavigator() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Rooms') {
            iconName = focused ? 'bed' : 'bed-outline';
          } else if (route.name === 'Reservations') {
            iconName = focused ? 'calendar' : 'calendar-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          } else {
            iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
      })}
    >
      <Tab.Screen name="Home" component={HomeStack} />
      <Tab.Screen name="Rooms" component={RoomsStack} />
      <Tab.Screen name="Reservations" component={ReservationsStack} />
      <Tab.Screen name="Profile" component={ProfileStack} />
    </Tab.Navigator>
  );
}
