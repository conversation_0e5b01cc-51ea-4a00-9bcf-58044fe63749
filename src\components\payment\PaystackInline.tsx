import React from 'react';
import { WebView } from 'react-native-webview';
import { View, StyleSheet } from 'react-native';
import { Portal, Modal } from 'react-native-paper';

interface PaystackInlineProps {
  publicKey: string;
  email: string;
  amount: number;
  currency: string;
  reference: string;
  onSuccess: (response: any) => void;
  onCancel: () => void;
  visible: boolean;
  customerName?: string;
  customerPhone?: string;
  metadata?: any;
}

export const PaystackInline: React.FC<PaystackInlineProps> = ({
  publicKey,
  email,
  amount,
  currency,
  reference,
  onSuccess,
  onCancel,
  visible,
  customerName,
  customerPhone,
  metadata
}) => {
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>Paystack Payment</title>
        <script src="https://js.paystack.co/v1/inline.js"></script>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f8f9fa;
            }
            .container {
                max-width: 400px;
                margin: 0 auto;
                background: white;
                border-radius: 12px;
                padding: 24px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            }
            .header {
                text-align: center;
                margin-bottom: 24px;
            }
            .amount {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 8px;
            }
            .description {
                color: #7f8c8d;
                font-size: 14px;
            }
            .pay-button {
                width: 100%;
                background: #00C851;
                color: white;
                border: none;
                padding: 16px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                margin-top: 20px;
            }
            .pay-button:hover {
                background: #00A041;
            }
            .cancel-button {
                width: 100%;
                background: transparent;
                color: #6c757d;
                border: 1px solid #dee2e6;
                padding: 12px;
                border-radius: 8px;
                font-size: 14px;
                cursor: pointer;
                margin-top: 12px;
            }
            .loading {
                text-align: center;
                padding: 40px;
                color: #6c757d;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="amount">${currency} ${(amount / 100).toLocaleString()}</div>
                <div class="description">Hotel Reservation Payment</div>
            </div>
            
            <button class="pay-button" onclick="payWithPaystack()">
                Pay with Paystack
            </button>
            
            <button class="cancel-button" onclick="cancelPayment()">
                Cancel Payment
            </button>
        </div>

        <script>
            function payWithPaystack() {
                // Force all payment channels to be available
                const handler = PaystackPop.setup({
                    key: '${publicKey}',
                    email: '${email}',
                    amount: ${amount},
                    currency: '${currency}',
                    ref: '${reference}',
                    firstname: '${customerName?.split(' ')[0] || ''}',
                    lastname: '${customerName?.split(' ').slice(1).join(' ') || ''}',
                    phone: '${customerPhone || ''}',
                    metadata: ${JSON.stringify(metadata || {})},
                    // Force all channels - this should override test mode limitations
                    channels: ['mobile_money', 'card', 'bank', 'ussd', 'qr', 'bank_transfer', 'eft'],
                    // Additional configuration to bypass test mode restrictions
                    bearer: 'account',
                    label: 'Hotel Reservation Payment',
                    // Force mobile money to be available
                    mobile_money: {
                        phone: '${customerPhone || '************'}'
                    },
                    callback: function(response) {
                        console.log('Paystack Success:', response);
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                            type: 'success',
                            data: response
                        }));
                    },
                    onClose: function() {
                        console.log('Paystack Closed');
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                            type: 'cancel'
                        }));
                    }
                });

                // Log the configuration for debugging
                console.log('Paystack Configuration:', {
                    key: '${publicKey}',
                    currency: '${currency}',
                    amount: ${amount},
                    channels: ['mobile_money', 'card', 'bank', 'ussd', 'qr', 'bank_transfer', 'eft']
                });

                handler.openIframe();
            }

            function cancelPayment() {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                    type: 'cancel'
                }));
            }

            // Auto-trigger payment on load
            setTimeout(() => {
                payWithPaystack();
            }, 1000);
        </script>
    </body>
    </html>
  `;

  const handleMessage = (event: any) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      
      if (data.type === 'success') {
        onSuccess(data.data);
      } else if (data.type === 'cancel') {
        onCancel();
      }
    } catch (error) {
      console.error('Error parsing WebView message:', error);
      onCancel();
    }
  };

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onCancel}
        contentContainerStyle={styles.modalContainer}
      >
      <View style={styles.container}>
        <WebView
          source={{ html: htmlContent }}
          onMessage={handleMessage}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          startInLoadingState={true}
          style={styles.webview}
        />
      </View>
      </Modal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    margin: 20,
    borderRadius: 8,
  },
  webview: {
    flex: 1,
  },
});
