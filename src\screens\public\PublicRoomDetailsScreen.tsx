import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Dimensions,
  Alert,
  TouchableOpacity,
  Image,
} from 'react-native';
import {
  Text,
  Button,
  Card,
  Chip,
  Surface,
  ActivityIndicator,
  Divider,
  IconButton,
} from 'react-native-paper';
import { useRoute, useNavigation } from '@react-navigation/native';
import { MaterialIcons, Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import PagerView from 'react-native-pager-view';

import { useRoomStore } from '../../store/roomStore';
import { colors, spacing, typography } from '../../constants';
import { formatPrice } from '../../utils/currency';
// import { ImageCarousel } from '../../components/ui/ImageCarousel';
import type { PublicNavigationProp } from '../../navigation/PublicNavigator';
import type { Room } from '../../types/database';

const { width } = Dimensions.get('window');

interface RouteParams {
  roomId: string;
  room?: Room;
}

export const PublicRoomDetailsScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const { roomId, room: routeRoom } = route.params as RouteParams;

  const { selectedRoom, fetchRoomById, loading } = useRoomStore();
  const [room, setRoom] = useState<Room | null>(routeRoom || null);
  const [imageIndex, setImageIndex] = useState(0);
  const pagerRef = useRef<PagerView>(null);

  useEffect(() => {
    if (!room) {
      fetchRoomById(roomId);
    }
  }, [roomId, room]);

  useEffect(() => {
    if (selectedRoom) {
      setRoom(selectedRoom);
    }
  }, [selectedRoom]);

  const handleBookNowPress = () => {
    if (!room) return;
    
    navigation.navigate('AuthPrompt', {
      action: 'booking',
      roomId: room.id,
      room: room,
    });
  };

  const getAmenityIcon = (amenity: string) => {
    const amenityIcons: { [key: string]: string } = {
      'wifi': 'wifi',
      'tv': 'tv',
      'ac': 'ac-unit',
      'minibar': 'local-bar',
      'balcony': 'balcony',
      'ocean-view': 'waves',
      'room-service': 'room-service',
      'safe': 'security',
      'bathroom': 'bathtub',
      'parking': 'local-parking',
    };
    return amenityIcons[amenity.toLowerCase()] || 'check-circle';
  };

  if (loading || !room) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Loading room details...</Text>
      </View>
    );
  }

  const renderImagePager = () => {
    const images = room.images && room.images.length > 0
      ? room.images
      : ['https://images.unsplash.com/photo-1611892440504-42a792e24d32?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80'];

    // Handle both legacy string arrays and new RoomImage objects
    const imageItems = images.map((image, index) => {
      if (typeof image === 'string') {
        // Legacy format - convert to RoomImage-like object
        return {
          id: `legacy_${index}`,
          url: image,
          alt_text: `Room ${room.room_number} image ${index + 1}`,
        };
      }
      // New format - already a RoomImage object
      return image;
    });

    return (
      <View style={styles.imageContainer}>
        <PagerView
          ref={pagerRef}
          style={styles.pager}
          initialPage={0}
          onPageSelected={(e) => setImageIndex(e.nativeEvent.position)}
        >
          {imageItems.map((image, index) => (
            <View key={image.id} style={styles.imagePage}>
              <Image
                source={{ uri: image.url }}
                style={styles.roomImage}
                resizeMode="cover"
                accessibilityLabel={image.alt_text || `Room image ${index + 1}`}
              />
            </View>
          ))}
        </PagerView>

        {imageItems.length > 1 && (
          <>
            <View style={styles.imageIndicators}>
              {imageItems.map((image, index) => (
                <View
                  key={`indicator_${image.id}`}
                  style={[
                    styles.indicator,
                    index === imageIndex && styles.activeIndicator
                  ]}
                />
              ))}
            </View>

            <IconButton
              icon="chevron-left"
              size={24}
              iconColor={colors.white}
              containerColor="rgba(0,0,0,0.5)"
              style={[styles.imageNavButton, styles.prevButton]}
              onPress={() => {
                const prevIndex = imageIndex > 0 ? imageIndex - 1 : imageItems.length - 1;
                pagerRef.current?.setPage(prevIndex);
              }}
            />

            <IconButton
              icon="chevron-right"
              size={24}
              iconColor={colors.white}
              containerColor="rgba(0,0,0,0.5)"
              style={[styles.imageNavButton, styles.nextButton]}
              onPress={() => {
                const nextIndex = imageIndex < imageItems.length - 1 ? imageIndex + 1 : 0;
                pagerRef.current?.setPage(nextIndex);
              }}
            />
          </>
        )}

        {/* Status Badge */}
        <View style={styles.statusBadge}>
          <Chip
            mode="flat"
            style={[
              styles.statusChip,
              { backgroundColor: room.status === 'available' ? colors.success : colors.error }
            ]}
            textStyle={styles.statusText}
          >
            {room.status === 'available' ? 'Available' : 'Not Available'}
          </Chip>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Room Images */}
        {renderImagePager()}

        <View style={styles.content}>
          {/* Room Header */}
          <View style={styles.header}>
            <View style={styles.titleContainer}>
              <Text style={styles.roomName}>Room {room.room_number}</Text>
              <Text style={styles.roomType}>{room.room_type.charAt(0).toUpperCase() + room.room_type.slice(1)} Room</Text>
            </View>
            <View style={styles.priceContainer}>
              <Text style={styles.price}>{formatPrice(room.price_per_night)}</Text>
              <Text style={styles.priceUnit}>per night</Text>
            </View>
          </View>

          {/* Room Info */}
          <Surface style={styles.infoCard}>
            <View style={styles.infoRow}>
              <View style={styles.infoItem}>
                <Ionicons name="people-outline" size={20} color={colors.primary} />
                <Text style={styles.infoText}>Up to {room.max_occupancy} guests</Text>
              </View>
              <View style={styles.infoItem}>
                <Ionicons name="bed-outline" size={20} color={colors.primary} />
                <Text style={styles.infoText}>King Size Bed</Text>
              </View>
            </View>
            <View style={styles.infoRow}>
              <View style={styles.infoItem}>
                <Ionicons name="resize-outline" size={20} color={colors.primary} />
                <Text style={styles.infoText}>45 m²</Text>
              </View>
              <View style={styles.infoItem}>
                <Ionicons name="location-outline" size={20} color={colors.primary} />
                <Text style={styles.infoText}>Ocean View</Text>
              </View>
            </View>
          </Surface>

          {/* Description */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.description}>
              {room.description || 'Experience luxury and comfort in this beautifully appointed room with modern amenities and stunning views.'}
            </Text>
          </View>

          {/* Amenities */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Amenities</Text>
            <View style={styles.amenitiesGrid}>
              {room.amenities?.map((amenity, index) => (
                <View key={index} style={styles.amenityItem}>
                  <MaterialIcons 
                    name={getAmenityIcon(amenity)} 
                    size={20} 
                    color={colors.primary} 
                  />
                  <Text style={styles.amenityText}>
                    {amenity.charAt(0).toUpperCase() + amenity.slice(1)}
                  </Text>
                </View>
              )) || (
                // Default amenities if none specified
                ['WiFi', 'TV', 'AC', 'Minibar', 'Room Service'].map((amenity, index) => (
                  <View key={index} style={styles.amenityItem}>
                    <MaterialIcons 
                      name={getAmenityIcon(amenity)} 
                      size={20} 
                      color={colors.primary} 
                    />
                    <Text style={styles.amenityText}>{amenity}</Text>
                  </View>
                ))
              )}
            </View>
          </View>

          {/* Policies */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Policies</Text>
            <View style={styles.policyItem}>
              <Ionicons name="time-outline" size={16} color={colors.onSurfaceVariant} />
              <Text style={styles.policyText}>Check-in: 3:00 PM</Text>
            </View>
            <View style={styles.policyItem}>
              <Ionicons name="time-outline" size={16} color={colors.onSurfaceVariant} />
              <Text style={styles.policyText}>Check-out: 11:00 AM</Text>
            </View>
            <View style={styles.policyItem}>
              <Ionicons name="close-circle-outline" size={16} color={colors.onSurfaceVariant} />
              <Text style={styles.policyText}>No smoking</Text>
            </View>
            <View style={styles.policyItem}>
              <Ionicons name="paw-outline" size={16} color={colors.onSurfaceVariant} />
              <Text style={styles.policyText}>Pet-friendly</Text>
            </View>
          </View>

          {/* Call to Action */}
          <Surface style={styles.ctaContainer}>
            <Text style={styles.ctaTitle}>Ready to Book?</Text>
            <Text style={styles.ctaSubtitle}>
              Sign in to check availability and make your reservation
            </Text>
            <Button
              mode="contained"
              onPress={handleBookNowPress}
              style={styles.bookButton}
              contentStyle={styles.bookButtonContent}
              disabled={room.status !== 'available'}
            >
              {room.status === 'available' ? 'Sign In to Book' : 'Not Available'}
            </Button>
          </Surface>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: spacing.md,
  },
  loadingText: {
    fontSize: typography.sizes.md,
    color: colors.onSurfaceVariant,
  },
  imageContainer: {
    position: 'relative',
    height: 300,
  },
  pager: {
    flex: 1,
    height: 300,
  },
  imagePage: {
    flex: 1,
  },
  roomImage: {
    width: '100%',
    height: '100%',
  },
  imageIndicators: {
    position: 'absolute',
    bottom: spacing.md,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: spacing.xs,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255,255,255,0.5)',
  },
  activeIndicator: {
    backgroundColor: colors.white,
  },
  imageNavButton: {
    position: 'absolute',
    top: '50%',
    marginTop: -20,
  },
  prevButton: {
    left: spacing.md,
  },
  nextButton: {
    right: spacing.md,
  },
  imagePlaceholder: {
    height: 300,
    backgroundColor: colors.surfaceVariant,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imagePlaceholderText: {
    fontSize: typography.sizes.lg,
    color: colors.onSurfaceVariant,
  },
  statusBadge: {
    position: 'absolute',
    top: spacing.md,
    right: spacing.md,
  },
  statusChip: {
    elevation: 2,
  },
  statusText: {
    color: colors.onPrimary,
    fontWeight: 'bold',
  },
  content: {
    padding: spacing.lg,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.lg,
  },
  titleContainer: {
    flex: 1,
  },
  roomName: {
    fontSize: typography.sizes.xxl,
    fontWeight: 'bold',
    color: colors.onBackground,
    marginBottom: spacing.xs,
  },
  roomType: {
    fontSize: typography.sizes.md,
    color: colors.onSurfaceVariant,
  },
  priceContainer: {
    alignItems: 'flex-end',
  },
  price: {
    fontSize: typography.sizes.xl,
    fontWeight: 'bold',
    color: colors.primary,
  },
  priceUnit: {
    fontSize: typography.sizes.sm,
    color: colors.onSurfaceVariant,
  },
  infoCard: {
    padding: spacing.lg,
    borderRadius: 12,
    marginBottom: spacing.lg,
    elevation: 1,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    flex: 1,
  },
  infoText: {
    fontSize: typography.sizes.sm,
    color: colors.onSurface,
  },
  section: {
    marginBottom: spacing.xl,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: 'bold',
    color: colors.onBackground,
    marginBottom: spacing.md,
  },
  description: {
    fontSize: typography.sizes.md,
    color: colors.onSurfaceVariant,
    lineHeight: 24,
  },
  amenitiesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.md,
  },
  amenityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    width: '48%',
    marginBottom: spacing.sm,
  },
  amenityText: {
    fontSize: typography.sizes.sm,
    color: colors.onSurface,
  },
  policyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
    marginBottom: spacing.sm,
  },
  policyText: {
    fontSize: typography.sizes.sm,
    color: colors.onSurfaceVariant,
  },
  ctaContainer: {
    padding: spacing.xl,
    borderRadius: 16,
    alignItems: 'center',
    elevation: 2,
    marginTop: spacing.lg,
  },
  ctaTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: 'bold',
    color: colors.onSurface,
    marginBottom: spacing.sm,
  },
  ctaSubtitle: {
    fontSize: typography.sizes.md,
    color: colors.onSurfaceVariant,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  bookButton: {
    borderRadius: 12,
    minWidth: 200,
  },
  bookButtonContent: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
  },
});
