import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../constants';
import type { PublicStackParamList } from '../types/navigation';

// Screen imports - Public versions
import { PublicHomeScreen } from '../screens/public/PublicHomeScreen';
import { PublicRoomsScreen } from '../screens/public/PublicRoomsScreen';
import { PublicRoomDetailsScreen } from '../screens/public/PublicRoomDetailsScreen';
import { AuthPromptScreen } from '../screens/public/AuthPromptScreen';

const Tab = createBottomTabNavigator();
const Stack = createNativeStackNavigator();

// Export navigation prop type for use in screens
export type PublicNavigationProp = {
  navigate: (screen: keyof PublicStackParamList, params?: any) => void;
  goBack: () => void;
  push: (screen: keyof PublicStackParamList, params?: any) => void;
  pop: (count?: number) => void;
  popToTop: () => void;
  setParams: (params: any) => void;
  addListener: (event: string, callback: () => void) => () => void;
  dispatch: (action: any) => void;
};

// Home Stack - Public browsing
function PublicHomeStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="HomeMain" 
        component={PublicHomeScreen} 
        options={{ title: 'Sunset View Hotel' }}
      />
      <Stack.Screen 
        name="RoomDetails" 
        component={PublicRoomDetailsScreen}
        options={{ title: 'Room Details' }}
      />
      <Stack.Screen 
        name="AuthPrompt" 
        component={AuthPromptScreen}
        options={{ 
          title: 'Sign In Required',
          presentation: 'modal'
        }}
      />
    </Stack.Navigator>
  );
}

// Rooms Stack - Public browsing
function PublicRoomsStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen 
        name="RoomsMain" 
        component={PublicRoomsScreen} 
        options={{ title: 'Available Rooms' }}
      />
      <Stack.Screen 
        name="RoomDetails" 
        component={PublicRoomDetailsScreen}
        options={{ title: 'Room Details' }}
      />
      <Stack.Screen 
        name="AuthPrompt" 
        component={AuthPromptScreen}
        options={{ 
          title: 'Sign In Required',
          presentation: 'modal'
        }}
      />
    </Stack.Navigator>
  );
}

// Auth Prompt Stack - Encourages sign in
function AuthPromptStack() {
  return (
    <Stack.Navigator>
      <Stack.Screen
        name="AuthPromptMain"
        component={AuthPromptScreen}
        options={{ title: 'Sign In' }}
        initialParams={{ action: 'profile' }}
      />
    </Stack.Navigator>
  );
}

export default function PublicNavigator() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Rooms') {
            iconName = focused ? 'bed' : 'bed-outline';
          } else if (route.name === 'SignIn') {
            iconName = focused ? 'person' : 'person-outline';
          } else {
            iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: 'gray',
        headerShown: false,
      })}
    >
      <Tab.Screen 
        name="Home" 
        component={PublicHomeStack}
        options={{ title: 'Home' }}
      />
      <Tab.Screen 
        name="Rooms" 
        component={PublicRoomsStack}
        options={{ title: 'Rooms' }}
      />
      <Tab.Screen 
        name="SignIn" 
        component={AuthPromptStack}
        options={{ title: 'Sign In' }}
      />
    </Tab.Navigator>
  );
}
