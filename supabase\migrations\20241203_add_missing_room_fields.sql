-- Migration to add missing fields to rooms table
-- This migration adds the missing fields that the app expects

-- Add missing columns to rooms table
ALTER TABLE public.rooms 
ADD COLUMN IF NOT EXISTS bed_type TEXT DEFAULT 'Queen Bed',
ADD COLUMN IF NOT EXISTS size_sqm DECIMAL(5,2) DEFAULT 25.0,
ADD COLUMN IF NOT EXISTS is_available BOOLEAN DEFAULT true NOT NULL;

-- Handle images column conversion from TEXT[] to JSONB
-- Check if images column is already JSONB
DO $$
DECLARE
    images_type text;
BEGIN
    -- Get the current data type of the images column
    SELECT data_type INTO images_type
    FROM information_schema.columns
    WHERE table_name = 'rooms'
    AND column_name = 'images'
    AND table_schema = 'public';

    -- Only convert if it's currently TEXT[] (ARRAY type)
    IF images_type = 'ARRAY' THEN
        RAISE NOTICE 'Converting images column from TEXT[] to JSONB...';

        -- Drop dependent views first
        DROP VIEW IF EXISTS public.rooms_enhanced CASCADE;
        DROP VIEW IF EXISTS public.rooms_with_image_ids CASCADE;

        -- Create a temporary column
        ALTER TABLE public.rooms ADD COLUMN images_temp JSONB DEFAULT '[]'::jsonb;

        -- Migrate existing data from TEXT[] to JSONB
        UPDATE public.rooms
        SET images_temp = CASE
          WHEN images IS NULL OR array_length(images, 1) IS NULL THEN '[]'::jsonb
          ELSE (
            SELECT jsonb_agg(
              jsonb_build_object(
                'id', 'legacy_' || gen_random_uuid()::text,
                'url', image_url,
                'alt_text', 'Room image',
                'upload_date', NOW()::text,
                'file_name', 'legacy_image.jpg',
                'file_size', null
              )
            )
            FROM unnest(images) AS image_url
          )
        END;

        -- Drop the old images column and rename the new one
        ALTER TABLE public.rooms DROP COLUMN images;
        ALTER TABLE public.rooms RENAME COLUMN images_temp TO images;

        RAISE NOTICE 'Images column conversion completed.';
    ELSE
        RAISE NOTICE 'Images column is already JSONB, skipping conversion.';
    END IF;
END $$;

-- Update the updated_at timestamp for all rooms
UPDATE public.rooms SET updated_at = NOW();

-- Create an index on room_type for better query performance
CREATE INDEX IF NOT EXISTS idx_rooms_room_type ON public.rooms(room_type);
CREATE INDEX IF NOT EXISTS idx_rooms_status ON public.rooms(status);
CREATE INDEX IF NOT EXISTS idx_rooms_is_available ON public.rooms(is_available);

-- Update any existing rooms to have proper is_available status based on their status
UPDATE public.rooms 
SET is_available = CASE 
  WHEN status = 'available' THEN true 
  ELSE false 
END;
