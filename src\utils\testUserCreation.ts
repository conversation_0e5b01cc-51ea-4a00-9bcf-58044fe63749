// Test utility to verify user creation fixes
// This can be used for debugging user creation issues

import { authService } from '../services/authService';

export const testUserCreation = async () => {
  console.log('🧪 Testing user creation fixes...');
  
  const testEmail = `test-${Date.now()}@example.com`;
  const testPassword = 'TestPassword123!';
  const testUserData = {
    full_name: 'Test User',
    phone: '+1234567890'
  };

  try {
    console.log('📝 Attempting to create test user...');
    
    // Test signup
    const signupResult = await authService.signUp(testEmail, testPassword, testUserData);
    
    if (signupResult.error) {
      console.error('❌ Signup failed:', signupResult.error);
      return { success: false, error: signupResult.error };
    }
    
    console.log('✅ Signup successful');
    
    // Wait a moment for the profile to be created
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Test signin to verify profile creation
    console.log('🔐 Testing signin...');
    const signinResult = await authService.signIn(testEmail, testPassword);
    
    if (signinResult.error) {
      console.error('❌ Signin failed:', signinResult.error);
      return { success: false, error: signinResult.error };
    }
    
    console.log('✅ Signin successful');
    
    // Test profile creation safety
    if (signinResult.data?.user) {
      console.log('👤 Testing profile safety...');
      await authService.ensureUserProfile(signinResult.data.user);
      console.log('✅ Profile safety test passed');
    }
    
    // Cleanup - sign out
    await authService.signOut();
    console.log('🚪 Signed out');
    
    console.log('🎉 All tests passed!');
    return { success: true };
    
  } catch (error) {
    console.error('💥 Test failed with error:', error);
    return { success: false, error };
  }
};

export const debugUserCreationError = (error: any) => {
  console.log('🔍 Debugging user creation error...');
  console.log('Error details:', {
    message: error?.message,
    code: error?.code,
    details: error?.details,
    hint: error?.hint
  });
  
  if (error?.code === '23505') {
    console.log('🔍 This is a duplicate key constraint violation');
    console.log('💡 Possible causes:');
    console.log('  - User already exists in the database');
    console.log('  - Race condition during user creation');
    console.log('  - Previous failed signup left partial data');
    console.log('💡 Solutions:');
    console.log('  - Use upsert instead of insert');
    console.log('  - Check if user exists before creating');
    console.log('  - Run the fix-duplicate-users.sql script');
  }
  
  if (error?.message?.includes('already registered')) {
    console.log('🔍 User already registered in auth.users');
    console.log('💡 This might be a profile creation issue');
  }
  
  return {
    isDuplicateKey: error?.code === '23505',
    isAlreadyRegistered: error?.message?.includes('already registered'),
    suggestedAction: error?.code === '23505' ? 'Use upsert or check existence' : 'Check auth state'
  };
};
