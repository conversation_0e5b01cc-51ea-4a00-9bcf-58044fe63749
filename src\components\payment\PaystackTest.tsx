import React, { useState } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { Button, Text, Card } from 'react-native-paper';
import { Paystack } from 'react-native-paystack-webview';
import { PAYSTACK_CONFIG } from '../../constants';
import { toCents } from '../../utils/currency';

export const PaystackTest = () => {
  const [showPaystack, setShowPaystack] = useState(false);
  const [testAmount] = useState(100); // KES 100 for testing

  const handlePayment = () => {
    setShowPaystack(true);
  };

  const handleSuccess = (response: any) => {
    setShowPaystack(false);
    console.log('Payment Success:', response);
    Alert.alert('Success', `Payment successful! Reference: ${response.reference}`);
  };

  const handleCancel = () => {
    setShowPaystack(false);
    Alert.alert('Cancelled', 'Payment was cancelled');
  };

  return (
    <View style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.title}>Paystack Payment Test</Text>
          <Text style={styles.subtitle}>Test M-Pesa & Mobile Money</Text>
          
          <Button
            mode="contained"
            onPress={handlePayment}
            style={styles.button}
          >
            Test Payment (KES 100)
          </Button>
        </Card.Content>
      </Card>

      {/* Test Configuration 1: Default channels */}
      {showPaystack && (
        <Paystack
          paystackKey={PAYSTACK_CONFIG.publicKey}
          amount={toCents(testAmount)}
          billingEmail="<EMAIL>"
          billingName="Test User"
          currency="KES"
          reference={`TEST_${Date.now()}`}
          onCancel={handleCancel}
          onSuccess={handleSuccess}
          autoStart={false}
          billingMobile="************"
          channels={['mobile_money', 'card', 'bank', 'ussd', 'qr', 'bank_transfer']}
          metadata={{
            test_mode: true,
            custom_fields: [
              {
                display_name: "Test Payment",
                variable_name: "test_payment",
                value: "true"
              }
            ]
          }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  card: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
  },
  button: {
    marginTop: 20,
  },
});
