{"name": "sunset-view-hotel", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "dev-client": "expo start --dev-client", "build:dev:android": "eas build --platform android --profile development", "build:dev:ios": "eas build --platform ios --profile development", "build:preview:android": "eas build --platform android --profile preview", "build:prod:android": "eas build --platform android --profile production", "typecheck": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "build:local:debug": "powershell -ExecutionPolicy Bypass -File ./scripts/setup-local-build.ps1 -BuildType debug", "build:local:release": "powershell -ExecutionPolicy Bypass -File ./scripts/setup-local-build.ps1 -BuildType release", "build:local:setup": "powershell -ExecutionPolicy Bypass -File ./scripts/setup-local-build.ps1 -SkipPrerequisites", "check:env": "powershell -ExecutionPolicy Bypass -File ./scripts/check-environment.ps1"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "@react-navigation/stack": "^6.4.1", "@supabase/supabase-js": "^2.45.4", "expo": "53.0.16", "expo-blur": "^14.1.5", "expo-constants": "~17.1.6", "expo-device": "~7.1.4", "expo-file-system": "^18.1.10", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "^14.1.5", "expo-notifications": "~0.31.3", "expo-print": "~14.1.4", "expo-sharing": "^13.1.5", "expo-status-bar": "~2.2.3", "react": "18.2.0", "react-native": "0.74.0", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-gifted-charts": "^1.4.61", "react-native-html-to-pdf": "^0.12.0", "react-native-modal-datetime-picker": "^18.0.0", "react-native-pager-view": "6.7.1", "react-native-paper": "^5.12.3", "react-native-paystack-webview": "^4.6.7", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.11.1", "react-native-super-grid": "^6.0.1", "react-native-svg": "15.11.2", "react-native-toast-message": "^2.2.1", "react-native-vector-icons": "^10.2.0", "zustand": "^5.0.5", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/expo": "^32.0.13", "@types/expo__vector-icons": "^9.0.1", "@types/node": "^22.15.30", "@types/react": "^18.2.6", "@types/react-native-vector-icons": "^6.4.18", "typescript": "~5.8.3"}, "private": true}