import React from 'react';
import { View } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { PaperProvider } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import Constants from 'expo-constants';
import Toast from 'react-native-toast-message';
import * as Notifications from 'expo-notifications';

import { AppNavigator } from './src/navigation/AppNavigator';
import { colors } from './src/constants';
import { NotificationService } from './src/services/notifications';
import { realtimeService } from './src/services/realtime';
import { useAuthStore } from './src/store/authStore';
import { useNotificationStore } from './src/store/notificationStore';
import { ThemeProvider, useTheme } from './src/contexts/ThemeContext';
import { lightPaperTheme, darkPaperTheme } from './src/themes/paperTheme';

// Configure notifications behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

function AppContent() {
  const { user, initialize } = useAuthStore();
  const { initializePushNotifications, subscribeToUpdates } = useNotificationStore();

  React.useEffect(() => {
    // Initialize auth state
    initialize();
  }, []);

  React.useEffect(() => {
    // Initialize push notifications
    initializePushNotifications();
  }, []);

  React.useEffect(() => {
    // Subscribe to real-time updates when user is authenticated
    if (user) {
      subscribeToUpdates(user.id, user.role);
      // RealtimeService doesn't have connect/disconnect methods
      // The subscriptions are handled individually
    }

    return () => {
      // Clean up handled by unsubscribeFromUpdates in the store
    };
  }, [user]);

  return (
    <>
      {/* Status bar background for edge-to-edge */}
      {Constants.platform?.android && (
        <View 
          style={{ 
            height: Constants.statusBarHeight, 
            backgroundColor: colors.primary 
          }} 
        />
      )}
      <AppNavigator />
      <Toast />
    </>
  );
}

function ThemedApp() {
  const { isDark } = useTheme();
  
  return (
    <PaperProvider theme={isDark ? darkPaperTheme : lightPaperTheme}>
      <AppContent />
    </PaperProvider>
  );
}

export default function App() {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <SafeAreaProvider>
        <ThemeProvider>
          <StatusBar style="light" translucent={true} />
          <ThemedApp />
        </ThemeProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}

