import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  Alert,
} from 'react-native';
import {
  Text,
  Searchbar,
  Button,
  Menu,
  Divider,
  Chip,
  Surface,
  ActivityIndicator,
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';

import { useRoomStore } from '../../store/roomStore';
import { colors, spacing, typography } from '../../constants';
import { RoomCard } from '../../components/cards/RoomCard';
import { formatPrice } from '../../utils/currency';
import type { Room, RoomFilter, RoomType, RoomStatus } from '../../types/database';
import type { PublicNavigationProp } from '../../navigation/PublicNavigator';

const ROOM_TYPES: { label: string; value: RoomType }[] = [
  { label: 'Standard', value: 'standard' },
  { label: 'Deluxe', value: 'deluxe' },
  { label: 'Suite', value: 'suite' },
  { label: 'Presidential', value: 'presidential' },
];

const PRICE_RANGES = [
  { label: 'Under KSh 15,000', min: 0, max: 15000 },
  { label: 'KSh 15,000 - KSh 25,000', min: 15000, max: 25000 },
  { label: 'KSh 25,000 - KSh 50,000', min: 25000, max: 50000 },
  { label: 'Above KSh 50,000', min: 50000, max: Infinity },
];

const OCCUPANCY_OPTIONS = [
  { label: '1 Guest', value: 1 },
  { label: '2 Guests', value: 2 },
  { label: '4 Guests', value: 4 },
  { label: '6+ Guests', value: 6 },
];

export const PublicRoomsScreen = () => {
  const navigation = useNavigation();
  const { rooms, fetchRooms, loading } = useRoomStore();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);
  const [filters, setFilters] = useState<RoomFilter>({});
  const [showFilters, setShowFilters] = useState(false);
  const [sortMenuVisible, setSortMenuVisible] = useState(false);
  const [sortBy, setSortBy] = useState<'price_asc' | 'price_desc' | 'type'>('price_asc');

  useEffect(() => {
    fetchRooms();
  }, []);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchRooms();
    setRefreshing(false);
  }, []);

  const handleRoomPress = (room: Room) => {
    navigation.navigate('RoomDetails', { roomId: room.id, room });
  };

  const applyFilters = (rooms: Room[]) => {
    let filtered = rooms.filter(room => room.status === 'available');

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(room =>
        room.room_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
        room.room_type.toLowerCase().includes(searchQuery.toLowerCase()) ||
        room.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Type filter
    if (filters.room_type) {
      filtered = filtered.filter(room => room.room_type === filters.room_type);
    }

    // Price filter
    if (filters.min_price !== undefined || filters.max_price !== undefined) {
      filtered = filtered.filter(room => {
        const price = room.price_per_night;
        const minPrice = filters.min_price || 0;
        const maxPrice = filters.max_price || Infinity;
        return price >= minPrice && price <= maxPrice;
      });
    }

    // Occupancy filter
    if (filters.max_occupancy) {
      filtered = filtered.filter(room => room.max_occupancy >= filters.max_occupancy!);
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price_asc':
          return a.price_per_night - b.price_per_night;
        case 'price_desc':
          return b.price_per_night - a.price_per_night;
        case 'type':
          return a.room_type.localeCompare(b.room_type);
        default:
          return 0;
      }
    });

    return filtered;
  };

  const filteredRooms = applyFilters(rooms);

  const clearFilters = () => {
    setFilters({});
    setSearchQuery('');
  };

  const hasActiveFilters = Object.keys(filters).length > 0 || searchQuery.length > 0;

  const renderRoomItem = ({ item }: { item: Room }) => (
    <View style={styles.roomItem}>
      <RoomCard
        room={item}
        onPress={() => handleRoomPress(item)}
      />
    </View>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <Text style={styles.title}>Available Rooms</Text>
      <Text style={styles.subtitle}>
        Browse our collection of luxury accommodations
      </Text>
      
      <Searchbar
        placeholder="Search rooms..."
        onChangeText={setSearchQuery}
        value={searchQuery}
        style={styles.searchbar}
      />

      <View style={styles.filterRow}>
        <Button
          mode={showFilters ? 'contained' : 'outlined'}
          onPress={() => setShowFilters(!showFilters)}
          icon="filter"
          style={styles.filterButton}
        >
          Filters
        </Button>

        <Menu
          visible={sortMenuVisible}
          onDismiss={() => setSortMenuVisible(false)}
          anchor={
            <Button
              mode="outlined"
              onPress={() => setSortMenuVisible(true)}
              icon="sort"
              style={styles.sortButton}
            >
              Sort
            </Button>
          }
        >
          <Menu.Item
            onPress={() => {
              setSortBy('price_asc');
              setSortMenuVisible(false);
            }}
            title="Price: Low to High"
          />
          <Menu.Item
            onPress={() => {
              setSortBy('price_desc');
              setSortMenuVisible(false);
            }}
            title="Price: High to Low"
          />
          <Menu.Item
            onPress={() => {
              setSortBy('type');
              setSortMenuVisible(false);
            }}
            title="Room Type"
          />
        </Menu>

        {hasActiveFilters && (
          <Button
            mode="text"
            onPress={clearFilters}
            textColor={colors.error}
            style={styles.clearButton}
          >
            Clear
          </Button>
        )}
      </View>

      {showFilters && (
        <Surface style={styles.filtersContainer}>
          <Text style={styles.filtersTitle}>Filter Options</Text>
          
          {/* Room Type Filter */}
          <View style={styles.filterSection}>
            <Text style={styles.filterLabel}>Room Type</Text>
            <View style={styles.chipContainer}>
              {ROOM_TYPES.map((type) => (
                <Chip
                  key={type.value}
                  selected={filters.room_type === type.value}
                  onPress={() =>
                    setFilters(prev => ({
                      ...prev,
                      room_type: prev.room_type === type.value ? undefined : type.value,
                    }))
                  }
                  style={styles.chip}
                >
                  {type.label}
                </Chip>
              ))}
            </View>
          </View>

          {/* Price Range Filter */}
          <View style={styles.filterSection}>
            <Text style={styles.filterLabel}>Price Range</Text>
            <View style={styles.chipContainer}>
              {PRICE_RANGES.map((range, index) => (
                <Chip
                  key={index}
                  selected={filters.min_price === range.min && filters.max_price === range.max}
                  onPress={() =>
                    setFilters(prev => ({
                      ...prev,
                      min_price: prev.min_price === range.min ? undefined : range.min,
                      max_price: prev.max_price === range.max ? undefined : range.max,
                    }))
                  }
                  style={styles.chip}
                >
                  {range.label}
                </Chip>
              ))}
            </View>
          </View>

          {/* Occupancy Filter */}
          <View style={styles.filterSection}>
            <Text style={styles.filterLabel}>Guests</Text>
            <View style={styles.chipContainer}>
              {OCCUPANCY_OPTIONS.map((option) => (
                <Chip
                  key={option.value}
                  selected={filters.max_occupancy === option.value}
                  onPress={() =>
                    setFilters(prev => ({
                      ...prev,
                      max_occupancy: prev.max_occupancy === option.value ? undefined : option.value,
                    }))
                  }
                  style={styles.chip}
                >
                  {option.label}
                </Chip>
              ))}
            </View>
          </View>
        </Surface>
      )}

      <View style={styles.resultsHeader}>
        <Text style={styles.resultsCount}>
          {filteredRooms.length} room{filteredRooms.length !== 1 ? 's' : ''} available
        </Text>
      </View>
    </View>
  );

  if (loading && rooms.length === 0) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Loading rooms...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={filteredRooms}
        renderItem={renderRoomItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
          />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <MaterialIcons name="hotel" size={64} color={colors.outline} />
            <Text style={styles.emptyTitle}>No rooms found</Text>
            <Text style={styles.emptySubtitle}>
              Try adjusting your search or filters
            </Text>
          </View>
        }
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: spacing.md,
  },
  loadingText: {
    fontSize: typography.sizes.md,
    color: colors.onSurfaceVariant,
  },
  listContainer: {
    paddingBottom: spacing.xl,
  },
  header: {
    padding: spacing.lg,
    gap: spacing.md,
  },
  title: {
    fontSize: typography.sizes.xxl,
    fontWeight: 'bold',
    color: colors.onBackground,
  },
  subtitle: {
    fontSize: typography.sizes.md,
    color: colors.onSurfaceVariant,
  },
  searchbar: {
    elevation: 2,
  },
  searchInput: {
    fontSize: typography.sizes.md,
  },
  filterRow: {
    flexDirection: 'row',
    gap: spacing.sm,
    alignItems: 'center',
  },
  filterButton: {
    flex: 1,
  },
  sortButton: {
    flex: 1,
  },
  clearButton: {
    marginLeft: spacing.sm,
  },
  filtersContainer: {
    padding: spacing.lg,
    borderRadius: 12,
    elevation: 1,
  },
  filtersTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: 'bold',
    marginBottom: spacing.md,
    color: colors.onSurface,
  },
  filterSection: {
    marginBottom: spacing.lg,
  },
  filterLabel: {
    fontSize: typography.sizes.md,
    fontWeight: '500',
    marginBottom: spacing.sm,
    color: colors.onSurface,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  },
  chip: {
    marginBottom: spacing.xs,
  },
  resultsHeader: {
    paddingTop: spacing.md,
  },
  resultsCount: {
    fontSize: typography.sizes.sm,
    color: colors.onSurfaceVariant,
    fontWeight: '500',
  },
  roomItem: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.md,
  },
  emptyContainer: {
    alignItems: 'center',
    padding: spacing.xl,
    gap: spacing.md,
  },
  emptyTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: 'bold',
    color: colors.onSurface,
  },
  emptySubtitle: {
    fontSize: typography.sizes.md,
    color: colors.onSurfaceVariant,
    textAlign: 'center',
  },
});
