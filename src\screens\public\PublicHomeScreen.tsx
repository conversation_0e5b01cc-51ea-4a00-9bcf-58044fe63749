import React from 'react';
import ReactNative from 'react-native';
const {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  ImageBackground,
  Dimensions,
  StatusBar,
  Animated,
  TouchableOpacity,
} = ReactNative;
import {
  Text,
  Card,
  Button,
  Chip,
  Divider,
  IconButton,
  Surface,
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { MaterialIcons, Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';

import { useRoomStore } from '../../store/roomStore';
import { Colors, Spacing, Typography, lightColors } from '../../constants';
import RoomCard from '../../components/cards/RoomCard';
import type { PublicNavigationProp } from '../../navigation/PublicNavigator';
import type { RoomType } from '../../types/models';

type Room = RoomType;

const { width } = Dimensions.get('window');

export const PublicHomeScreen = () => {
  const navigation = useNavigation();
  const { rooms, fetchRooms, loading: roomsLoading } = useRoomStore();
  const [refreshing, setRefreshing] = React.useState(false);
  const scrollY = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    fetchRooms();
  }, []);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    await fetchRooms();
    setRefreshing(false);
  }, []);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const handleRoomPress = (room: Room) => {
    navigation.navigate('RoomDetails', { roomId: room.id, room });
  };

  const handleSignInPress = () => {
    navigation.navigate('AuthPrompt', { action: 'booking' });
  };

  const featuredRooms = rooms.slice(0, 3);
  const availableRooms = rooms.filter(room => room.status === 'available');

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      <Animated.ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: false }
        )}
        scrollEventThrottle={16}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[lightColors.primary]}
            tintColor={lightColors.primary}
          />
        }
      >
        {/* Hero Section */}
        <ImageBackground
          source={{
            uri: 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80'
          }}
          style={styles.heroSection}
          imageStyle={styles.heroImage}
        >
          <LinearGradient
            colors={['rgba(0,0,0,0.3)', 'rgba(0,0,0,0.7)']}
            style={styles.heroGradient}
          >
            <View style={styles.heroContent}>
              <View style={styles.welcomeContainer}>
                <View style={styles.welcomeText}>
                  <Text style={styles.greeting}>{getGreeting()},</Text>
                  <Text style={styles.userName}>Welcome to Sunset View Hotel</Text>
                  <Text style={styles.subtitle}>
                    Discover luxury accommodations and book your perfect stay
                  </Text>
                </View>
              </View>

              <Surface style={styles.quickActions}>
                <Text style={styles.quickActionsTitle}>Quick Actions</Text>
                <View style={styles.actionButtons}>
                  <TouchableOpacity 
                    style={styles.actionButton}
                    onPress={() => navigation.navigate('Rooms')}
                  >
                    <Ionicons name="bed-outline" size={24} color={lightColors.primary} />
                    <Text style={styles.actionButtonText}>Browse Rooms</Text>
                  </TouchableOpacity>
                  <TouchableOpacity 
                    style={styles.actionButton}
                    onPress={handleSignInPress}
                  >
                    <Ionicons name="person-outline" size={24} color={lightColors.primary} />
                    <Text style={styles.actionButtonText}>Sign In</Text>
                  </TouchableOpacity>
                </View>
              </Surface>
            </View>
          </LinearGradient>
        </ImageBackground>

        {/* Hotel Stats */}
        <Surface style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{availableRooms.length}</Text>
            <Text style={styles.statLabel}>Available Rooms</Text>
          </View>
          <Divider style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>4.8</Text>
            <Text style={styles.statLabel}>Guest Rating</Text>
          </View>
          <Divider style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>24/7</Text>
            <Text style={styles.statLabel}>Service</Text>
          </View>
        </Surface>

        {/* Featured Rooms */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Featured Rooms</Text>
            <Button 
              mode="text" 
              onPress={() => navigation.navigate('Rooms')}
              textColor={lightColors.primary}
            >
              View All
            </Button>
          </View>
          
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.roomsScrollContainer}
          >
            {featuredRooms.map((room) => (
              <View key={room.id} style={styles.roomCardContainer}>
                <RoomCard
                  room={room}
                  onPress={() => handleRoomPress(room)}
                  style={styles.roomCard}
                />
              </View>
            ))}
          </ScrollView>
        </View>

        {/* Call to Action */}
        <Surface style={styles.ctaContainer}>
          <Text style={styles.ctaTitle}>Ready to Book?</Text>
          <Text style={styles.ctaSubtitle}>
            Sign in or create an account to make reservations and manage your bookings
          </Text>
          <Button
            mode="contained"
            onPress={handleSignInPress}
            style={styles.ctaButton}
            contentStyle={styles.ctaButtonContent}
          >
            Get Started
          </Button>
        </Surface>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </Animated.ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: lightColors.background,
  },
  scrollView: {
    flex: 1,
  },
  heroSection: {
    height: 400,
    justifyContent: 'flex-end',
  },
  heroImage: {
    resizeMode: 'cover',
  },
  heroGradient: {
    flex: 1,
    justifyContent: 'flex-end',
    paddingHorizontal: Spacing.lg,
    paddingBottom: Spacing.xl,
  },
  heroContent: {
    gap: Spacing.lg,
  },
  welcomeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
  },
  welcomeText: {
    flex: 1,
  },
  greeting: {
    fontSize: Typography.sizes.lg,
    color: Colors.white,
    fontWeight: '500',
  },
  userName: {
    fontSize: Typography.sizes.xl,
    color: Colors.white,
    fontWeight: 'bold',
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: Typography.sizes.md,
    color: Colors.white,
    opacity: 0.9,
  },
  quickActions: {
    borderRadius: 16,
    padding: Spacing.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
  },
  quickActionsTitle: {
    fontSize: Typography.sizes.lg,
    fontWeight: 'bold',
    marginBottom: Spacing.md,
    color: lightColors.onSurface,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  actionButton: {
    flex: 1,
    alignItems: 'center',
    padding: Spacing.md,
    borderRadius: 12,
    backgroundColor: lightColors.surface,
    borderWidth: 1,
    borderColor: lightColors.outline,
  },
  actionButtonText: {
    marginTop: Spacing.xs,
    fontSize: Typography.sizes.sm,
    color: lightColors.onSurface,
    fontWeight: '500',
  },
  statsContainer: {
    flexDirection: 'row',
    margin: Spacing.lg,
    padding: Spacing.lg,
    borderRadius: 16,
    elevation: 2,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: Typography.sizes.xxl,
    fontWeight: 'bold',
    color: lightColors.primary,
  },
  statLabel: {
    fontSize: Typography.sizes.sm,
    color: lightColors.onSurfaceVariant,
    marginTop: Spacing.xs,
  },
  statDivider: {
    width: 1,
    height: '100%',
    marginHorizontal: Spacing.md,
  },
  section: {
    marginBottom: Spacing.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
  },
  sectionTitle: {
    fontSize: Typography.sizes.xl,
    fontWeight: 'bold',
    color: lightColors.onBackground,
  },
  roomsScrollContainer: {
    paddingHorizontal: Spacing.lg,
  },
  roomCardContainer: {
    marginRight: Spacing.md,
  },
  roomCard: {
    width: width * 0.8,
  },
  ctaContainer: {
    margin: Spacing.lg,
    padding: Spacing.xl,
    borderRadius: 16,
    alignItems: 'center',
    elevation: 2,
  },
  ctaTitle: {
    fontSize: Typography.sizes.xl,
    fontWeight: 'bold',
    color: lightColors.onSurface,
    marginBottom: Spacing.sm,
  },
  ctaSubtitle: {
    fontSize: Typography.sizes.md,
    color: lightColors.onSurfaceVariant,
    textAlign: 'center',
    marginBottom: Spacing.lg,
  },
  ctaButton: {
    borderRadius: 12,
  },
  ctaButtonContent: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
  },
  bottomSpacing: {
    height: Spacing.xl,
  },
});
