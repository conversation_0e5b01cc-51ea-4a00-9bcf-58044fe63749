import React, { useState } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { WebView } from 'react-native-webview';
import { Button, Text, Card, Portal, Modal } from 'react-native-paper';

interface PaystackAdvancedProps {
  publicKey: string;
  email: string;
  amount: number;
  currency: string;
  reference: string;
  onSuccess: (response: any) => void;
  onCancel: () => void;
  visible: boolean;
  customerName?: string;
  customerPhone?: string;
  metadata?: any;
}

export const PaystackAdvanced: React.FC<PaystackAdvancedProps> = ({
  publicKey,
  email,
  amount,
  currency,
  reference,
  onSuccess,
  onCancel,
  visible,
  customerName,
  customerPhone,
  metadata
}) => {
  const [loading, setLoading] = useState(true);

  // Advanced HTML with forced payment methods
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>Paystack Payment</title>
        <script src="https://js.paystack.co/v1/inline.js"></script>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                margin: 0;
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .container {
                background: white;
                border-radius: 16px;
                padding: 32px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                text-align: center;
                max-width: 400px;
                width: 100%;
            }
            .title {
                font-size: 24px;
                font-weight: bold;
                color: #333;
                margin-bottom: 8px;
            }
            .amount {
                font-size: 32px;
                font-weight: bold;
                color: #2E7D32;
                margin-bottom: 16px;
            }
            .description {
                color: #666;
                margin-bottom: 24px;
                line-height: 1.5;
            }
            .pay-button {
                background: #2E7D32;
                color: white;
                border: none;
                padding: 16px 32px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                cursor: pointer;
                width: 100%;
                margin-bottom: 16px;
            }
            .pay-button:hover {
                background: #1B5E20;
            }
            .cancel-button {
                background: #f5f5f5;
                color: #666;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-size: 14px;
                cursor: pointer;
                width: 100%;
            }
            .methods {
                margin-top: 16px;
                padding: 16px;
                background: #f8f9fa;
                border-radius: 8px;
                font-size: 12px;
                color: #666;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="title">Hotel Payment</div>
            <div class="amount">${currency} ${(amount / 100).toLocaleString()}</div>
            <div class="description">
                Pay securely with M-Pesa, Cards, Bank Transfer, or USSD
            </div>
            
            <button class="pay-button" onclick="payWithPaystack()">
                🇰🇪 Pay with M-Pesa & More
            </button>
            
            <button class="cancel-button" onclick="cancelPayment()">
                Cancel Payment
            </button>
            
            <div class="methods">
                Available: M-Pesa • Airtel Money • Cards • Bank Transfer • USSD
            </div>
        </div>

        <script>
            function payWithPaystack() {
                console.log('🚀 Initializing Paystack with all payment methods...');
                
                try {
                    const handler = PaystackPop.setup({
                        key: '${publicKey}',
                        email: '${email}',
                        amount: ${amount},
                        currency: '${currency}',
                        ref: '${reference}',
                        firstname: '${customerName?.split(' ')[0] || ''}',
                        lastname: '${customerName?.split(' ').slice(1).join(' ') || ''}',
                        phone: '${customerPhone || '************'}',
                        metadata: ${JSON.stringify(metadata || {})},
                        
                        // FORCE ALL PAYMENT METHODS - Multiple approaches
                        channels: ['mobile_money', 'card', 'bank', 'ussd', 'qr', 'bank_transfer', 'eft'],
                        
                        // Additional configuration to force mobile money
                        mobile_money: {
                            phone: '${customerPhone || '************'}'
                        },
                        
                        // Force account bearer to enable more options
                        bearer: 'account',
                        
                        // Custom label
                        label: 'Sunset View Hotel Payment',
                        
                        callback: function(response) {
                            console.log('✅ Payment Success:', response);
                            window.ReactNativeWebView.postMessage(JSON.stringify({
                                type: 'success',
                                data: response
                            }));
                        },
                        
                        onClose: function() {
                            console.log('❌ Payment Closed');
                            // Don't auto-cancel, let user decide
                        }
                    });
                    
                    console.log('📱 Opening Paystack popup...');
                    handler.openIframe();
                    
                } catch (error) {
                    console.error('❌ Paystack Error:', error);
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                        type: 'error',
                        message: error.message
                    }));
                }
            }
            
            function cancelPayment() {
                console.log('❌ Payment cancelled by user');
                window.ReactNativeWebView.postMessage(JSON.stringify({
                    type: 'cancel'
                }));
            }
            
            // Auto-start payment when page loads
            window.onload = function() {
                console.log('🔄 Page loaded, ready for payment');
                // Don't auto-start, let user click button
            };
        </script>
    </body>
    </html>
  `;

  const handleMessage = (event: any) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      console.log('📨 Message from WebView:', data);
      
      switch (data.type) {
        case 'success':
          onSuccess(data.data);
          break;
        case 'cancel':
          onCancel();
          break;
        case 'error':
          Alert.alert('Payment Error', data.message);
          onCancel();
          break;
      }
    } catch (error) {
      console.error('❌ Error parsing WebView message:', error);
    }
  };

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onCancel}
        contentContainerStyle={styles.modalContainer}
      >
      <View style={styles.container}>
        <WebView
          source={{ html: htmlContent }}
          onMessage={handleMessage}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          startInLoadingState={true}
          onLoadStart={() => setLoading(true)}
          onLoadEnd={() => setLoading(false)}
          style={styles.webview}
          // Additional props to ensure compatibility
          mixedContentMode="compatibility"
          allowsInlineMediaPlayback={true}
          mediaPlaybackRequiresUserAction={false}
        />
      </View>
      </Modal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    margin: 20,
    borderRadius: 8,
  },
  webview: {
    flex: 1,
  },
});
