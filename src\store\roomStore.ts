import { create } from 'zustand';
import { roomService } from '../services/supabase';
import type { Room, RoomFilter } from '../types';

interface RoomStore {
  // State
  rooms: Room[];
  selectedRoom: Room | null;
  loading: boolean;
  error: string | null;
  filters: RoomFilter;

  // Actions
  fetchRooms: (filters?: RoomFilter) => Promise<void>;
  fetchRoomById: (id: string) => Promise<void>;
  createRoom: (roomData: Partial<Room>) => Promise<{ success: boolean; error?: string }>;
  addRoom: (roomData: Partial<Room>) => Promise<{ success: boolean; error?: string }>; // Alias for createRoom
  updateRoom: (id: string, roomData: Partial<Room>) => Promise<{ success: boolean; error?: string }>;
  deleteRoom: (id: string) => Promise<{ success: boolean; error?: string }>;
  checkAvailability: (roomId: string, checkIn: string, checkOut: string) => Promise<boolean>;
  setSelectedRoom: (room: Room | null) => void;
  setFilters: (filters: RoomFilter) => void;
  clearError: () => void;
  reset: () => void;
}

export const useRoomStore = create((set: any, get: any) => ({
  // Initial state
  rooms: [],
  selectedRoom: null,
  loading: false,
  error: null,
  filters: {},

  // Actions
  fetchRooms: async (filters?: RoomFilter) => {
    set({ loading: true, error: null });
    try {
      const { data, error } = await roomService.getRooms(filters);

      if (error) {
        set({ error: error.message, loading: false });
        return;
      }

      set({ rooms: data || [], loading: false });
    } catch (error) {
      set({ error: 'Failed to fetch rooms', loading: false });
    }
  },

  fetchRoomById: async (id: string) => {
    set({ loading: true, error: null });
    try {
      const { data, error } = await roomService.getRoomById(id);
      
      if (error) {
        set({ error: error.message, loading: false });
        return;
      }

      set({ selectedRoom: data, loading: false });
    } catch (error) {
      set({ error: 'Failed to fetch room details', loading: false });
    }
  },

  createRoom: async (roomData: Partial<Room>) => {
    set({ loading: true, error: null });
    try {
      const { data, error } = await roomService.createRoom(roomData);

      if (error) {
        set({ error: error.message, loading: false });
        return { success: false, error: error.message };
      }

      // Add new room to the list
      const currentRooms = get().rooms;
      set({
        rooms: [...currentRooms, data],
        loading: false
      });

      return { success: true };
    } catch (error) {
      set({ error: 'Failed to create room', loading: false });
      return { success: false, error: 'Failed to create room' };
    }
  },

  // Alias for createRoom to maintain compatibility
  addRoom: async (roomData: Partial<Room>) => {
    return get().createRoom(roomData);
  },

  updateRoom: async (id: string, roomData: Partial<Room>) => {
    set({ loading: true, error: null });
    try {
      const { data, error } = await roomService.updateRoom(id, roomData);
      
      if (error) {
        set({ error: error.message, loading: false });
        return { success: false, error: error.message };
      }

      // Update room in the list
      const currentRooms = get().rooms;
      const updatedRooms = currentRooms.map(room => 
        room.id === id ? data : room
      );
      
      set({ 
        rooms: updatedRooms,
        selectedRoom: get().selectedRoom?.id === id ? data : get().selectedRoom,
        loading: false 
      });

      return { success: true };
    } catch (error) {
      set({ error: 'Failed to update room', loading: false });
      return { success: false, error: 'Failed to update room' };
    }
  },

  deleteRoom: async (id: string) => {
    set({ loading: true, error: null });
    try {
      const { error } = await roomService.deleteRoom(id);
      
      if (error) {
        set({ error: error.message, loading: false });
        return { success: false, error: error.message };
      }

      // Remove room from the list
      const currentRooms = get().rooms;
      const filteredRooms = currentRooms.filter(room => room.id !== id);
      
      set({ 
        rooms: filteredRooms,
        selectedRoom: get().selectedRoom?.id === id ? null : get().selectedRoom,
        loading: false 
      });

      return { success: true };
    } catch (error) {
      set({ error: 'Failed to delete room', loading: false });
      return { success: false, error: 'Failed to delete room' };
    }
  },

  checkAvailability: async (roomId: string, checkIn: string, checkOut: string) => {
    try {
      const { data, error } = await roomService.checkAvailability(roomId, checkIn, checkOut);
      
      if (error) {
        console.error('Error checking availability:', error);
        return false;
      }

      return data || false;
    } catch (error) {
      console.error('Failed to check availability:', error);
      return false;
    }
  },

  setSelectedRoom: (room: Room | null) => {
    set({ selectedRoom: room });
  },

  setFilters: (filters: RoomFilter) => {
    set({ filters });
  },

  clearError: () => {
    set({ error: null });
  },

  reset: () => {
    set({
      rooms: [],
      selectedRoom: null,
      loading: false,
      error: null,
      filters: {},
    });
  },
}));
