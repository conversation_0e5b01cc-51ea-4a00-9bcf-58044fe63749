// Test script to verify room creation works
// This can be run in the browser console or as a Node.js script

const testRoomData = {
  room_number: 'TEST123',
  type: 'standard', // This should be mapped to room_type in the service
  bed_type: 'Queen Bed',
  size_sqm: 25.0,
  price_per_night: 8500.00,
  description: 'Test room to verify creation works',
  amenities: ['WiFi', 'Air Conditioning', 'TV'],
  max_occupancy: 2,
  status: 'available',
  is_available: true,
  images: [
    {
      id: 'test_img_1',
      url: 'https://images.unsplash.com/photo-1566665797739-1674de7a421a?w=800&q=80',
      alt_text: 'Test room image',
      upload_date: new Date().toISOString(),
      file_name: 'test_room.jpg',
      file_size: null
    }
  ]
};

console.log('Test room data:', testRoomData);
console.log('This data should be successfully created in the database with the schema fixes.');

// Expected database mapping:
const expectedDbData = {
  room_number: testRoomData.room_number,
  room_type: testRoomData.type, // type -> room_type
  bed_type: testRoomData.bed_type,
  size_sqm: testRoomData.size_sqm,
  price_per_night: testRoomData.price_per_night,
  description: testRoomData.description,
  amenities: testRoomData.amenities,
  max_occupancy: testRoomData.max_occupancy,
  status: testRoomData.status,
  is_available: testRoomData.is_available,
  images: testRoomData.images
};

console.log('Expected database data:', expectedDbData);
