import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { authService } from '../services/authService';
import { permissionsService } from '../services/permissionsService';
import type { User, AuthState } from '../types';
import type { Room } from '../types/database';

interface PendingBookingIntent {
  roomId?: string;
  room?: Room;
  checkIn?: string;
  checkOut?: string;
  guests?: number;
  returnTo?: 'booking' | 'reservation' | 'profile';
}

interface AuthActions {
  signIn: (email: string, password: string, bookingIntent?: PendingBookingIntent) => Promise<{ success: boolean; error?: string }>;
  signUp: (email: string, password: string, userData: { full_name: string; phone?: string }, bookingIntent?: PendingBookingIntent) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ success: boolean; error?: string }>;
  updatePassword: (password: string) => Promise<{ success: boolean; error?: string }>;
  updateProfile: (userData: Partial<User>) => Promise<{ success: boolean; error?: string }>;
  setUser: (user: User | null) => void;
  setSession: (session: any) => void;
  setLoading: (loading: boolean) => void;
  initialize: () => Promise<void>;
  setPendingBookingIntent: (intent: PendingBookingIntent | null) => void;
  clearPendingBookingIntent: () => void;
}

interface AuthStore extends AuthState, AuthActions {
  pendingBookingIntent: PendingBookingIntent | null;
}

export const useAuthStore = create(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      session: null,
      loading: true,
      pendingBookingIntent: null,

      // Actions
      signIn: async (email: string, password: string, bookingIntent?: PendingBookingIntent) => {
        set({ loading: true });
        try {
          const { data, error } = await authService.signIn(email, password);

          if (error) {
            set({ loading: false });
            return { success: false, error: error.message };
          }

          if (data.user && data.session) {
            // Get user profile from our users table to ensure role sync
            const userProfile = data.user.user_metadata as User;

            // Try to get the most up-to-date role from public.users table
            let actualRole = userProfile.role || 'guest';
            try {
              const { data: dbUser } = await authService.getUserProfile(data.user.id);
              if (dbUser?.role) {
                actualRole = dbUser.role;
                console.log('🔄 Using role from database:', actualRole);
              }
            } catch (dbError) {
              console.log('📝 Using role from JWT metadata:', actualRole);
            }

            const user = {
              id: data.user.id,
              email: data.user.email!,
              full_name: userProfile.full_name || '',
              phone: userProfile.phone,
              role: actualRole,
              created_at: data.user.created_at,
              updated_at: data.user.updated_at || data.user.created_at,
            };

            // Initialize permissions service
            permissionsService.initialize(user.id, user.role);

            set({
              user,
              session: data.session,
              loading: false,
              pendingBookingIntent: bookingIntent || null,
            });
            return { success: true };
          }

          set({ loading: false });
          return { success: false, error: 'Authentication failed' };
        } catch (error) {
          set({ loading: false });
          return { success: false, error: 'Network error occurred' };
        }
      },

      signUp: async (email: string, password: string, userData: { full_name: string; phone?: string }, bookingIntent?: PendingBookingIntent) => {
        set({ loading: true });
        try {
          const { error } = await authService.signUp(email, password, userData);

          if (error) {
            console.error('Auth store signup error:', error);
            set({ loading: false });

            // Provide more specific error messages
            let errorMessage = error.message || 'Failed to create account';

            if (errorMessage.includes('already registered')) {
              errorMessage = 'An account with this email already exists';
            } else if (errorMessage.includes('invalid email')) {
              errorMessage = 'Please enter a valid email address';
            } else if (errorMessage.includes('weak password')) {
              errorMessage = 'Password is too weak. Please choose a stronger password';
            } else if (errorMessage.includes('Database error')) {
              errorMessage = 'Database error saving new user. Please try again or contact support.';
            } else if (errorMessage.includes('duplicate key') || error.code === '23505') {
              errorMessage = 'Account creation failed due to a duplicate entry. Please try signing in instead or contact support.';
            }

            return { success: false, error: errorMessage };
          }

          set({
            loading: false,
            pendingBookingIntent: bookingIntent || null,
          });
          return { success: true };
        } catch (error: any) {
          console.error('Auth store signup catch error:', error);
          set({ loading: false });

          // Handle specific error cases in catch block too
          let errorMessage = error?.message || 'Network error occurred';

          if (errorMessage.includes('duplicate key') || error?.code === '23505') {
            errorMessage = 'Account creation failed due to a duplicate entry. Please try signing in instead or contact support.';
          } else if (errorMessage.includes('already registered')) {
            errorMessage = 'An account with this email already exists. Please try signing in instead.';
          }

          return { success: false, error: errorMessage };
        }
      },

      signOut: async () => {
        set({ loading: true });
        try {
          await authService.signOut();

          // Clear permissions service
          permissionsService.clear();

          set({
            user: null,
            session: null,
            loading: false,
            pendingBookingIntent: null,
          });
        } catch (error) {
          set({ loading: false });
        }
      },

      resetPassword: async (email: string) => {
        try {
          const { error } = await authService.resetPassword(email);
          
          if (error) {
            return { success: false, error: error.message };
          }

          return { success: true };
        } catch (error) {
          return { success: false, error: 'Network error occurred' };
        }
      },

      updatePassword: async (password: string) => {
        try {
          const { error } = await authService.updatePassword(password);

          if (error) {
            return { success: false, error: error.message };
          }

          return { success: true };
        } catch (error) {
          return { success: false, error: 'Network error occurred' };
        }
      },

      updateProfile: async (userData: Partial<User>) => {
        try {
          const currentUser = (get() as AuthStore).user;
          if (!currentUser) {
            return { success: false, error: 'No user logged in' };
          }

          // Update user metadata in Supabase Auth
          const { error } = await authService.updateUser({
            data: userData
          });

          if (error) {
            return { success: false, error: error.message };
          }

          // Update local state
          set({
            user: {
              ...currentUser,
              ...userData,
              updated_at: new Date().toISOString(),
            }
          });

          return { success: true };
        } catch (error) {
          return { success: false, error: 'Network error occurred' };
        }
      },

      setUser: (user: User | null) => {
        set({ user });
      },

      setSession: (session: any) => {
        set({ session });
      },

      setLoading: (loading: boolean) => {
        set({ loading });
      },

      initialize: async () => {
        set({ loading: true });
        try {
          const { data, error } = await authService.getSession();

          if (error || !data.session) {
            set({ user: null, session: null, loading: false });
            return;
          }

          // Get user profile and sync role
          const userProfile = data.session.user.user_metadata as User;

          // Try to get the most up-to-date role from public.users table
          let actualRole = userProfile.role || 'guest';
          try {
            const { data: dbUser } = await authService.getUserProfile(data.session.user.id);
            if (dbUser?.role) {
              actualRole = dbUser.role;
              console.log('🔄 Initialize: Using role from database:', actualRole);
            }
          } catch (dbError) {
            console.log('📝 Initialize: Using role from JWT metadata:', actualRole);
          }

          const user = {
            id: data.session.user.id,
            email: data.session.user.email!,
            full_name: userProfile.full_name || '',
            phone: userProfile.phone,
            role: actualRole,
            created_at: data.session.user.created_at,
            updated_at: data.session.user.updated_at || data.session.user.created_at,
          };

          // Initialize permissions service
          permissionsService.initialize(user.id, user.role);

          set({
            user,
            session: data.session,
            loading: false,
          });

          // Set up auth state listener
          authService.onAuthStateChange((event, session) => {
            if (event === 'SIGNED_OUT' || !session) {
              set({ user: null, session: null, pendingBookingIntent: null });
            } else if (event === 'SIGNED_IN' && session) {
              const userProfile = session.user.user_metadata as User;
              set({
                user: {
                  id: session.user.id,
                  email: session.user.email!,
                  full_name: userProfile.full_name || '',
                  phone: userProfile.phone,
                  role: userProfile.role || 'guest',
                  created_at: session.user.created_at,
                  updated_at: session.user.updated_at || session.user.created_at,
                },
                session,
              });
            }
          });
        } catch (error) {
          set({ user: null, session: null, loading: false });
        }
      },

      setPendingBookingIntent: (intent: PendingBookingIntent | null) => {
        set({ pendingBookingIntent: intent });
      },

      clearPendingBookingIntent: () => {
        set({ pendingBookingIntent: null });
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state: any) => {
        const { user, session, loading, pendingBookingIntent } = state;
        return { user, session, loading, pendingBookingIntent };
      },
    } as any
  )
);
