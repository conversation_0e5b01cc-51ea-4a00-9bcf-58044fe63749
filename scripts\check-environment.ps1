# Environment Checker for Local APK Build
# This script checks if all prerequisites are properly installed

$Green = [System.ConsoleColor]::Green
$Red = [System.ConsoleColor]::Red
$Yellow = [System.ConsoleColor]::Yellow
$Blue = [System.ConsoleColor]::Blue

function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Test-Command($command) {
    try {
        Get-Command $command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

Write-ColorOutput $Blue "🔍 Environment Check for Local APK Build"
Write-Output "========================================"
Write-Output ""

$allGood = $true

# Check Node.js
Write-Output "Checking Node.js..."
if (Test-Command "node") {
    $nodeVersion = node --version
    Write-ColorOutput $Green "✅ Node.js: $nodeVersion"
    
    # Check if version is 18+
    $versionNumber = [int]($nodeVersion -replace 'v(\d+)\..*', '$1')
    if ($versionNumber -lt 18) {
        Write-ColorOutput $Yellow "⚠️  Node.js version should be 18 or higher"
    }
} else {
    Write-ColorOutput $Red "❌ Node.js not found"
    Write-Output "   Install from: https://nodejs.org/"
    $allGood = $false
}

# Check package manager
Write-Output ""
Write-Output "Checking package manager..."
if (Test-Command "yarn") {
    $yarnVersion = yarn --version
    Write-ColorOutput $Green "✅ Yarn: v$yarnVersion"
} elseif (Test-Command "npm") {
    $npmVersion = npm --version
    Write-ColorOutput $Green "✅ npm: v$npmVersion"
} else {
    Write-ColorOutput $Red "❌ No package manager found"
    $allGood = $false
}

# Check Java
Write-Output ""
Write-Output "Checking Java JDK..."
if (Test-Command "java") {
    try {
        $javaVersion = java -version 2>&1 | Select-String "version"
        Write-ColorOutput $Green "✅ Java: $javaVersion"
        
        # Check JAVA_HOME
        if ($env:JAVA_HOME) {
            Write-ColorOutput $Green "✅ JAVA_HOME: $env:JAVA_HOME"
        } else {
            Write-ColorOutput $Yellow "⚠️  JAVA_HOME not set"
        }
    } catch {
        Write-ColorOutput $Red "❌ Java found but version check failed"
        $allGood = $false
    }
} else {
    Write-ColorOutput $Red "❌ Java not found"
    Write-Output "   Install JDK 11 or 17 from: https://adoptium.net/temurin/releases/"
    $allGood = $false
}

# Check Android SDK
Write-Output ""
Write-Output "Checking Android SDK..."
if ($env:ANDROID_HOME) {
    Write-ColorOutput $Green "✅ ANDROID_HOME: $env:ANDROID_HOME"
    
    # Check if ADB exists
    $adbPath = "$env:ANDROID_HOME\platform-tools\adb.exe"
    if (Test-Path $adbPath) {
        Write-ColorOutput $Green "✅ ADB found"
    } else {
        Write-ColorOutput $Red "❌ ADB not found at: $adbPath"
        $allGood = $false
    }
    
    # Check if SDK tools exist
    $sdkManagerPath = "$env:ANDROID_HOME\cmdline-tools\latest\bin\sdkmanager.bat"
    if (Test-Path $sdkManagerPath) {
        Write-ColorOutput $Green "✅ SDK Manager found"
    } else {
        Write-ColorOutput $Yellow "⚠️  SDK Manager not found (may need to install cmdline-tools)"
    }
} else {
    Write-ColorOutput $Red "❌ ANDROID_HOME not set"
    Write-Output "   Install Android Studio and set ANDROID_HOME"
    $allGood = $false
}

# Check Expo CLI
Write-Output ""
Write-Output "Checking Expo CLI..."
if (Test-Command "expo") {
    try {
        $expoVersion = expo --version
        Write-ColorOutput $Green "✅ Expo CLI: v$expoVersion"
    } catch {
        Write-ColorOutput $Yellow "⚠️  Expo CLI found but version check failed"
    }
} else {
    Write-ColorOutput $Yellow "⚠️  Expo CLI not found (will be installed by build script)"
}

# Check Git
Write-Output ""
Write-Output "Checking Git..."
if (Test-Command "git") {
    $gitVersion = git --version
    Write-ColorOutput $Green "✅ Git: $gitVersion"
} else {
    Write-ColorOutput $Yellow "⚠️  Git not found (recommended for version control)"
}

# Check PowerShell version
Write-Output ""
Write-Output "Checking PowerShell..."
$psVersion = $PSVersionTable.PSVersion
Write-ColorOutput $Green "✅ PowerShell: v$psVersion"

# Summary
Write-Output ""
Write-Output "========================================"
if ($allGood) {
    Write-ColorOutput $Green "🎉 All prerequisites are installed!"
    Write-Output "You can now run the build script."
} else {
    Write-ColorOutput $Red "❌ Some prerequisites are missing."
    Write-Output "Please install the missing components before building."
}

Write-Output ""
Write-Output "Next steps:"
Write-Output "1. Fix any missing prerequisites above"
Write-Output "2. Run: .\scripts\build-apk.bat"
Write-Output "3. Or run: .\scripts\setup-local-build.ps1"
