import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { reservationService, paymentService } from '../services/supabase';
import type { Reservation, BookingFormData } from '../types';

interface ReservationStore {
  // State
  reservations: Reservation[];
  userReservations: Reservation[];
  selectedReservation: Reservation | null;
  loading: boolean;
  error: string | null;

  // Actions
  fetchUserReservations: (userId: string) => Promise<void>;
  fetchAllReservations: () => Promise<void>;
  fetchReservationById: (id: string) => Promise<void>;
  createReservation: (reservationData: BookingFormData & { guest_id: string; total_amount: number }) => Promise<{ success: boolean; reservation?: Reservation; error?: string }>;
  updateReservation: (id: string, reservationData: Partial<Reservation>) => Promise<{ success: boolean; error?: string }>;
  cancelReservation: (id: string) => Promise<{ success: boolean; error?: string }>;
  setSelectedReservation: (reservation: Reservation | null) => void;
  clearError: () => void;
  reset: () => void;
}

export const useReservationStore = create(
  persist(
    (set, get) => ({
      // Initial state
      reservations: [],
      userReservations: [],
      selectedReservation: null,
      loading: false,
      error: null,

      // Actions
      fetchUserReservations: async (userId: string) => {
        set({ loading: true, error: null });
        try {
          const { data, error } = await reservationService.getUserReservations(userId);
          
          if (error) {
            set({ error: error.message, loading: false });
            return;
          }

          set({ userReservations: data || [], loading: false });
        } catch (error) {
          set({ error: 'Failed to fetch reservations', loading: false });
        }
      },

      fetchAllReservations: async () => {
        set({ loading: true, error: null });
        try {
          const { data, error } = await reservationService.getAllReservations();
          
          if (error) {
            set({ error: error.message, loading: false });
            return;
          }

          set({ reservations: data || [], loading: false });
        } catch (error) {
          set({ error: 'Failed to fetch reservations', loading: false });
        }
      },

      fetchReservationById: async (id: string) => {
        set({ loading: true, error: null });
        try {
          // Find reservation in existing lists first
          const existingReservation = get().userReservations.find(r => r.id === id) || 
                                      get().reservations.find(r => r.id === id);
          
          if (existingReservation) {
            set({ selectedReservation: existingReservation, loading: false });
            return;
          }
          
          // If not found locally, fetch from API
          const { data: reservation, error } = await reservationService.getReservationById(id);
          
          if (error) {
            set({ error: error.message, loading: false });
            return;
          }

          set({ selectedReservation: reservation || null, loading: false });
        } catch (error) {
          set({ error: 'Failed to fetch reservation details', loading: false });
        }
      },

      createReservation: async (reservationData: BookingFormData & { guest_id: string; total_amount: number }) => {
        set({ loading: true, error: null });
        try {
          // Create reservation
          const { data: reservation, error: reservationError } = await reservationService.createReservation({
            ...reservationData,
            status: 'pending',
            payment_status: 'pending',
          });
          
          if (reservationError) {
            set({ error: reservationError.message, loading: false });
            return { success: false, error: reservationError.message };
          }

          // Create payment record
          const { data: payment, error: paymentError } = await paymentService.createPayment({
            reservation_id: reservation.id,
            amount: reservationData.total_amount,
            currency: 'NGN',
            payment_method: 'card',
            status: 'pending',
          });

          if (paymentError) {
            // If payment creation fails, we should handle this appropriately
            console.error('Payment creation failed:', paymentError);
          }

          // Update reservation with payment ID
          if (payment) {
            await reservationService.updateReservation(reservation.id, {
              payment_id: payment.id,
            });
          }

          // Add new reservation to user reservations
          const currentUserReservations = get().userReservations;
          set({ 
            userReservations: [reservation, ...currentUserReservations],
            loading: false 
          });

          return { success: true, reservation };
        } catch (error) {
          set({ error: 'Failed to create reservation', loading: false });
          return { success: false, error: 'Failed to create reservation' };
        }
      },

      updateReservation: async (id: string, reservationData: Partial<Reservation>) => {
        set({ loading: true, error: null });
        try {
          const { data, error } = await reservationService.updateReservation(id, reservationData);
          
          if (error) {
            set({ error: error.message, loading: false });
            return { success: false, error: error.message };
          }

          // Update reservation in both lists
          const currentReservations = get().reservations;
          const currentUserReservations = get().userReservations;
          
          const updatedReservations = currentReservations.map(reservation => 
            reservation.id === id ? data : reservation
          );
          
          const updatedUserReservations = currentUserReservations.map(reservation => 
            reservation.id === id ? data : reservation
          );
          
          set({ 
            reservations: updatedReservations,
            userReservations: updatedUserReservations,
            selectedReservation: get().selectedReservation?.id === id ? data : get().selectedReservation,
            loading: false 
          });

          return { success: true };
        } catch (error) {
          set({ error: 'Failed to update reservation', loading: false });
          return { success: false, error: 'Failed to update reservation' };
        }
      },

      cancelReservation: async (id: string) => {
        set({ loading: true, error: null });
        try {
          const { data, error } = await reservationService.cancelReservation(id);
          
          if (error) {
            set({ error: error.message, loading: false });
            return { success: false, error: error.message };
          }

          // Update reservation in both lists
          const currentReservations = get().reservations;
          const currentUserReservations = get().userReservations;
          
          const updatedReservations = currentReservations.map(reservation => 
            reservation.id === id ? data : reservation
          );
          
          const updatedUserReservations = currentUserReservations.map(reservation => 
            reservation.id === id ? data : reservation
          );
          
          set({ 
            reservations: updatedReservations,
            userReservations: updatedUserReservations,
            selectedReservation: get().selectedReservation?.id === id ? data : get().selectedReservation,
            loading: false 
          });

          return { success: true };
        } catch (error) {
          set({ error: 'Failed to cancel reservation', loading: false });
          return { success: false, error: 'Failed to cancel reservation' };
        }
      },

      setSelectedReservation: (reservation) => set({ selectedReservation: reservation }),

      clearError: () => set({ error: null }),

      reset: () =>
        set({
          reservations: [],
          userReservations: [],
          selectedReservation: null,
          loading: false,
          error: null,
        }),
    }),
    {
      name: 'reservation-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);
