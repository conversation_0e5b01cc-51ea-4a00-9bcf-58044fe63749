import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  Chip,
  Surface,
  ActivityIndicator,
  FAB,
  Menu,
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { useFocusEffect } from '@react-navigation/native';
import { MaterialIcons } from '@expo/vector-icons';

import { useReservationStore } from '../../store/reservationStore';
import { useAuthStore } from '../../store/authStore';
import { colors, spacing, typography } from '../../constants';
import { formatPrice } from '../../utils/currency';
import type { ReservationWithDetails, ReservationStatus } from '../../types/database';

const STATUS_FILTERS = [
  { label: 'All', value: undefined },
  { label: 'Pending', value: 'pending' as ReservationStatus },
  { label: 'Confirmed', value: 'confirmed' as ReservationStatus },
  { label: 'Checked In', value: 'checked_in' as ReservationStatus },
  { label: 'Checked Out', value: 'checked_out' as ReservationStatus },
  { label: 'Cancelled', value: 'cancelled' as ReservationStatus },
];

export const ReservationsScreen = () => {
  const navigation = useNavigation();
  const { user } = useAuthStore();
  const { 
    userReservations, 
    loading, 
    error, 
    fetchUserReservations, 
    cancelReservation,
    clearError 
  } = useReservationStore();
  
  const [refreshing, setRefreshing] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<ReservationStatus | undefined>(undefined);
  const [filterMenuVisible, setFilterMenuVisible] = useState(false);

  useFocusEffect(
    useCallback(() => {
      if (user) {
        loadReservations();
      }
    }, [user])
  );

  useEffect(() => {
    if (error) {
      Alert.alert('Error', error, [
        { text: 'Retry', onPress: loadReservations },
        { text: 'OK', onPress: clearError }
      ]);
    }
  }, [error]);

  const loadReservations = async () => {
    if (user) {
      await fetchUserReservations(user.id);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadReservations();
    setRefreshing(false);
  };

  const filteredReservations = React.useMemo(() => {
    if (!selectedStatus) return userReservations;
    return userReservations.filter(reservation => reservation.status === selectedStatus);
  }, [userReservations, selectedStatus]);

  const getStatusColor = (status: ReservationStatus) => {
    switch (status) {
      case 'pending':
        return colors.warning;
      case 'confirmed':
        return colors.info;
      case 'checked_in':
        return colors.success;
      case 'checked_out':
        return colors.textSecondary;
      case 'cancelled':
        return colors.error;
      default:
        return colors.textSecondary;
    }
  };

  const getStatusIcon = (status: ReservationStatus) => {
    switch (status) {
      case 'pending':
        return 'schedule';
      case 'confirmed':
        return 'check-circle-outline';
      case 'checked_in':
        return 'login';
      case 'checked_out':
        return 'logout';
      case 'cancelled':
        return 'cancel';
      default:
        return 'help-outline';
    }
  };



  const canCancelReservation = (reservation: ReservationWithDetails) => {
    const now = new Date();
    const checkIn = new Date(reservation.check_in);
    const hoursUntilCheckIn = (checkIn.getTime() - now.getTime()) / (1000 * 60 * 60);
    
    return reservation.status === 'confirmed' && hoursUntilCheckIn > 24;
  };

  const handleCancelReservation = (reservation: ReservationWithDetails) => {
    Alert.alert(
      'Cancel Reservation',
      `Are you sure you want to cancel your reservation for Room ${reservation.room?.room_number}?`,
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes, Cancel',
          style: 'destructive',
          onPress: async () => {
            const result = await cancelReservation(reservation.id);
            if (result.success) {
              Alert.alert('Success', 'Your reservation has been cancelled');
            } else {
              Alert.alert('Error', result.error || 'Failed to cancel reservation');
            }
          }
        }
      ]
    );
  };

  const renderReservationItem = ({ item }: { item: ReservationWithDetails }) => {
    const checkIn = new Date(item.check_in);
    const checkOut = new Date(item.check_out);
    const nights = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24));

    return (
      <Card style={styles.reservationCard}>
        <Card.Content>
          {/* Header */}
          <View style={styles.reservationHeader}>
            <View style={styles.roomInfo}>
              <Text style={styles.roomNumber}>
                Room {item.room?.room_number || 'N/A'}
              </Text>
              <Text style={styles.roomType}>
                {item.room?.room_type || 'Unknown'}
              </Text>
            </View>
            
            <Chip
              mode="outlined"
              icon={getStatusIcon(item.status)}
              textStyle={[styles.statusText, { color: getStatusColor(item.status) }]}
              style={[styles.statusChip, { borderColor: getStatusColor(item.status) }]}
              compact={false}
            >
              {item.status}
            </Chip>
          </View>

          {/* Dates */}
          <View style={styles.datesContainer}>
            <View style={styles.dateItem}>
              <MaterialIcons name="login" size={16} color={colors.textSecondary} />
              <Text style={styles.dateText}>
                Check-in: {checkIn.toLocaleDateString()}
              </Text>
            </View>
            <View style={styles.dateItem}>
              <MaterialIcons name="logout" size={16} color={colors.textSecondary} />
              <Text style={styles.dateText}>
                Check-out: {checkOut.toLocaleDateString()}
              </Text>
            </View>
          </View>

          {/* Details */}
          <View style={styles.detailsContainer}>
            <View style={styles.detailItem}>
              <MaterialIcons name="people" size={16} color={colors.textSecondary} />
              <Text style={styles.detailText}>{item.guests} guest{item.guests !== 1 ? 's' : ''}</Text>
            </View>
            <View style={styles.detailItem}>
              <MaterialIcons name="nights-stay" size={16} color={colors.textSecondary} />
              <Text style={styles.detailText}>{nights} night{nights !== 1 ? 's' : ''}</Text>
            </View>
            <View style={styles.detailItem}>
              <MaterialIcons name="payments" size={16} color={colors.textSecondary} />
              <Text style={styles.detailText}>{formatPrice(item.total_amount)}</Text>
            </View>
          </View>

          {/* Special Requests */}
          {item.special_requests && (
            <View style={styles.specialRequestsContainer}>
              <Text style={styles.specialRequestsLabel}>Special Requests:</Text>
              <Text style={styles.specialRequestsText}>{item.special_requests}</Text>
            </View>
          )}

          {/* Actions */}
          <View style={styles.actionsContainer}>
            <Button
              mode="contained"
              onPress={() => navigation.navigate('ReservationDetails', {
                reservationId: item.id,
                reservation: item
              })}
              compact
              style={[styles.actionButton, styles.detailsButton]}
              buttonColor={colors.primary}
              textColor="white"
            >
              View Details
            </Button>
            <Button
              mode="outlined"
              onPress={() => navigation.navigate('RoomDetails', { roomId: item.room_id })}
              compact
              style={styles.actionButton}
            >
              View Room
            </Button>

            {canCancelReservation(item) && (
              <Button
                mode="outlined"
                onPress={() => handleCancelReservation(item)}
                compact
                style={[styles.actionButton, styles.cancelButton]}
                textColor={colors.error}
              >
                Cancel
              </Button>
            )}
          </View>
        </Card.Content>
      </Card>
    );
  };

  const renderHeader = () => (
    <Surface style={styles.headerCard}>
      <View style={styles.headerContent}>
        <Text style={styles.headerTitle}>My Reservations</Text>
        <Menu
          visible={filterMenuVisible}
          onDismiss={() => setFilterMenuVisible(false)}
          anchor={
            <Button
              mode="outlined"
              onPress={() => setFilterMenuVisible(true)}
              icon="filter-variant"
              compact
            >
              {STATUS_FILTERS.find(f => f.value === selectedStatus)?.label || 'All'}
            </Button>
          }
        >
          {STATUS_FILTERS.map((filter) => (
            <Menu.Item
              key={filter.label}
              onPress={() => {
                setSelectedStatus(filter.value);
                setFilterMenuVisible(false);
              }}
              title={filter.label}
              leadingIcon={selectedStatus === filter.value ? "check" : undefined}
            />
          ))}
        </Menu>
      </View>
      
      <Text style={styles.headerSubtitle}>
        {filteredReservations.length} reservation{filteredReservations.length !== 1 ? 's' : ''}
      </Text>
    </Surface>
  );

  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <MaterialIcons name="event-available" size={64} color={colors.textSecondary} />
      <Text style={styles.emptyTitle}>No Reservations Found</Text>
      <Text style={styles.emptyMessage}>
        {selectedStatus 
          ? `You don't have any ${selectedStatus} reservations`
          : "You haven't made any reservations yet"
        }
      </Text>
      <Button
        mode="contained"
        onPress={() => navigation.navigate('Rooms')}
        style={styles.browseRoomsButton}
      >
        Browse Rooms
      </Button>
    </View>
  );

  if (loading && userReservations.length === 0) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Loading reservations...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={filteredReservations}
        renderItem={renderReservationItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmpty}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
          />
        }
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
      />
      
      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => navigation.navigate('Rooms')}
        label="New Booking"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  loadingText: {
    ...typography.body,
    marginTop: spacing.md,
    color: colors.textSecondary,
  },
  listContent: {
    paddingBottom: spacing.xl + 60, // Account for FAB
  },
  headerCard: {
    margin: spacing.md,
    padding: spacing.md,
    borderRadius: 8,
    elevation: 2,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  headerTitle: {
    ...typography.h3,
    fontWeight: 'bold',
    color: colors.textPrimary,
  },
  headerSubtitle: {
    ...typography.body,
    color: colors.textSecondary,
  },
  reservationCard: {
    marginHorizontal: spacing.md,
    marginBottom: spacing.md,
  },
  reservationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  roomInfo: {
    flex: 1,
  },
  roomNumber: {
    ...typography.h4,
    fontWeight: 'bold',
    color: colors.textPrimary,
    marginBottom: 2,
  },
  roomType: {
    ...typography.body,
    color: colors.primary,
    textTransform: 'capitalize',
  },
  statusChip: {
    marginLeft: spacing.md,
    minWidth: 90,
    height: 32,
    paddingHorizontal: 8,
  },
  statusText: {
    fontSize: 12,
    textTransform: 'capitalize',
    fontWeight: '500',
  },
  datesContainer: {
    marginBottom: spacing.md,
    gap: spacing.sm,
  },
  dateItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  dateText: {
    ...typography.body,
    color: colors.textSecondary,
  },
  detailsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.md,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  detailText: {
    ...typography.caption,
    color: colors.textSecondary,
  },
  specialRequestsContainer: {
    marginBottom: spacing.md,
    padding: spacing.sm,
    backgroundColor: colors.surface,
    borderRadius: 4,
  },
  specialRequestsLabel: {
    ...typography.caption,
    color: colors.textSecondary,
    marginBottom: 2,
  },
  specialRequestsText: {
    ...typography.body,
    color: colors.textPrimary,
  },
  actionsContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  actionButton: {
    flex: 1,
  },
  detailsButton: {
    backgroundColor: colors.primary,
    elevation: 2,
  },
  cancelButton: {
    borderColor: colors.error,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xl,
  },
  emptyTitle: {
    ...typography.h3,
    fontWeight: 'bold',
    marginTop: spacing.md,
    marginBottom: spacing.sm,
    color: colors.textPrimary,
  },
  emptyMessage: {
    ...typography.body,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },
  browseRoomsButton: {
    marginTop: spacing.md,
  },
  fab: {
    position: 'absolute',
    margin: spacing.md,
    right: 0,
    bottom: 0,
    backgroundColor: colors.accent, // Using gold color for better visibility
    elevation: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.4,
    shadowRadius: 12,
    borderRadius: 28,
    minWidth: 140,
    height: 56,
    borderWidth: 2,
    borderColor: colors.primary, // Add a border for extra contrast
  },
});
