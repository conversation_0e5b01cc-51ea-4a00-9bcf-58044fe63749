# Local APK Build Setup for Sunset View Hotel

This directory contains scripts to build APK files locally on Windows without using EAS Build.

## 🚀 Quick Start

### Option 1: Using Batch File (Recommended for beginners)
1. Double-click `build-apk.bat`
2. Select your build type from the menu
3. Wait for the build to complete

### Option 2: Using PowerShell Directly
```powershell
# Debug build (default)
.\scripts\setup-local-build.ps1

# Release build
.\scripts\setup-local-build.ps1 -BuildType release

# Build only (skip setup)
.\scripts\setup-local-build.ps1 -BuildOnly

# Setup only (no build)
.\scripts\setup-local-build.ps1 -SkipPrerequisites
```

## 📋 Prerequisites

The script will check and guide you through installing:

1. **Node.js 18+** - [Download here](https://nodejs.org/)
2. **Java JDK 11 or 17** - [Download here](https://adoptium.net/temurin/releases/)
3. **Android Studio** - [Download here](https://developer.android.com/studio)
4. **Android SDK** (comes with Android Studio)

## 🔧 Environment Variables

Make sure these are set in your Windows environment:

```
ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
JAVA_HOME=C:\Program Files\Java\jdk-17
```

Add to PATH:
- `%ANDROID_HOME%\platform-tools`
- `%ANDROID_HOME%\tools`
- `%JAVA_HOME%\bin`

## 📱 Build Types

### Debug Build
- Faster compilation
- Larger file size
- Includes debugging information
- Good for testing

### Release Build
- Optimized and minified
- Smaller file size
- Production-ready
- Requires signing for distribution

## 📁 Output Location

After successful build, you'll find:
- APK file in project root: `sunset-view-hotel-debug.apk` or `sunset-view-hotel-release.apk`
- Original APK in: `android/app/build/outputs/apk/[debug|release]/`

## 🔍 Troubleshooting

### Common Issues:

1. **"Java not found"**
   - Install JDK 11 or 17
   - Set JAVA_HOME environment variable

2. **"Android SDK not found"**
   - Install Android Studio
   - Set ANDROID_HOME environment variable
   - Accept SDK licenses: `%ANDROID_HOME%\tools\bin\sdkmanager --licenses`

3. **"Gradle build failed"**
   - Clean build: `cd android && .\gradlew clean`
   - Check Android SDK components are installed

4. **"Metro bundler issues"**
   - Clear Metro cache: `npx expo start --clear`
   - Delete node_modules and reinstall

### Manual Steps if Script Fails:

```bash
# 1. Install dependencies
npm install

# 2. Generate native code
npx expo prebuild --platform android --clear

# 3. Build APK
cd android
.\gradlew assembleDebug
```

## 🔐 Signing Release APK

For production release, you'll need to sign the APK:

1. Generate keystore:
```bash
keytool -genkey -v -keystore sunset-view-hotel.keystore -alias sunset-view-hotel -keyalg RSA -keysize 2048 -validity 10000
```

2. Configure signing in `android/app/build.gradle`

3. Build signed APK:
```bash
.\gradlew assembleRelease
```

## 📊 Build Information

- **Project**: Sunset View Hotel Reservation App
- **Expo SDK**: 53
- **React Native**: 0.74.0
- **Target Android**: API 34
- **Min Android**: API 21

## 🆘 Getting Help

If you encounter issues:
1. Check the console output for specific error messages
2. Ensure all prerequisites are properly installed
3. Try cleaning and rebuilding: `npx expo prebuild --clear`
4. Check Android Studio SDK Manager for missing components

## 📝 Notes

- First build may take 10-15 minutes
- Subsequent builds are faster (3-5 minutes)
- Debug builds are ~50MB, release builds are ~25MB
- The script automatically copies the APK to project root for easy access
