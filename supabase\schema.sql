-- Sunset View Hotel Database Schema

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON><PERSON> custom types
CREATE TYPE user_role AS ENUM ('guest', 'receptionist', 'admin');
CREATE TYPE room_type AS ENUM ('standard', 'deluxe', 'suite', 'presidential');
CREATE TYPE room_status AS ENUM ('available', 'booked', 'maintenance', 'cleaning');
CREATE TYPE reservation_status AS ENUM ('pending', 'confirmed', 'checked_in', 'checked_out', 'cancelled');
CREATE TYPE payment_status AS ENUM ('pending', 'paid', 'failed', 'refunded');
CREATE TYPE payment_method AS ENUM ('card', 'bank_transfer', 'cash', 'pos');

-- Users table (extends auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    phone TEXT,
    role user_role DEFAULT 'guest' NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Rooms table
CREATE TABLE public.rooms (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    room_number TEXT UNIQUE NOT NULL,
    room_type room_type NOT NULL,
    bed_type TEXT DEFAULT 'Queen Bed',
    size_sqm DECIMAL(5,2) DEFAULT 25.0,
    price_per_night DECIMAL(10,2) NOT NULL CHECK (price_per_night > 0),
    description TEXT NOT NULL,
    amenities TEXT[] DEFAULT '{}',
    max_occupancy INTEGER NOT NULL CHECK (max_occupancy > 0),
    images JSONB DEFAULT '[]'::jsonb,
    status room_status DEFAULT 'available' NOT NULL,
    is_available BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Reservations table
CREATE TABLE public.reservations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    guest_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    room_id UUID REFERENCES public.rooms(id) ON DELETE CASCADE NOT NULL,
    check_in_date DATE NOT NULL,
    check_out_date DATE NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount > 0),
    special_requests TEXT,
    status reservation_status DEFAULT 'pending' NOT NULL,
    payment_status payment_status DEFAULT 'pending' NOT NULL,
    payment_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT valid_dates CHECK (check_out_date > check_in_date)
);

-- Payments table
CREATE TABLE public.payments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    reservation_id UUID REFERENCES public.reservations(id) ON DELETE CASCADE NOT NULL,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    currency TEXT DEFAULT 'KES' NOT NULL,
    payment_method payment_method NOT NULL,
    paystack_reference TEXT,
    paystack_transaction_id TEXT,
    status payment_status DEFAULT 'pending' NOT NULL,
    paid_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add foreign key constraint for payment_id in reservations
ALTER TABLE public.reservations 
ADD CONSTRAINT fk_reservations_payment_id 
FOREIGN KEY (payment_id) REFERENCES public.payments(id);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON public.users(email);
CREATE INDEX idx_users_role ON public.users(role);
CREATE INDEX idx_rooms_status ON public.rooms(status);
CREATE INDEX idx_rooms_type ON public.rooms(room_type);
CREATE INDEX idx_reservations_guest_id ON public.reservations(guest_id);
CREATE INDEX idx_reservations_room_id ON public.reservations(room_id);
CREATE INDEX idx_reservations_dates ON public.reservations(check_in_date, check_out_date);
CREATE INDEX idx_reservations_status ON public.reservations(status);
CREATE INDEX idx_payments_reservation_id ON public.payments(reservation_id);
CREATE INDEX idx_payments_status ON public.payments(status);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rooms_updated_at BEFORE UPDATE ON public.rooms
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reservations_updated_at BEFORE UPDATE ON public.reservations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payments_updated_at BEFORE UPDATE ON public.payments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Note: User profile creation is now handled manually in the application
-- This avoids trigger-related issues with RLS policies

-- Row Level Security (RLS) Policies

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.rooms ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reservations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;

-- Users policies
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

-- Allow INSERT for service role and authenticated users (for manual profile creation)
CREATE POLICY "Enable insert for service role and authenticated users" ON public.users
    FOR INSERT WITH CHECK (
        auth.role() = 'service_role' OR
        auth.role() = 'authenticated' OR
        auth.uid() = id
    );

-- Note: Removed recursive policies that caused infinite recursion
-- Role-based access will be handled through JWT claims or application logic

-- Rooms policies
CREATE POLICY "Anyone can view available rooms" ON public.rooms
    FOR SELECT USING (true);

-- Temporarily disable admin-only room management to avoid recursion
-- This will be handled through application logic
CREATE POLICY "Only admins can manage rooms" ON public.rooms
    FOR ALL USING (false);

-- Reservations policies
CREATE POLICY "Users can view their own reservations" ON public.reservations
    FOR SELECT USING (auth.uid() = guest_id);

CREATE POLICY "Users can create their own reservations" ON public.reservations
    FOR INSERT WITH CHECK (auth.uid() = guest_id);

CREATE POLICY "Users can update their own reservations" ON public.reservations
    FOR UPDATE USING (auth.uid() = guest_id);

-- Temporarily disable staff-only reservation access to avoid recursion
-- This will be handled through application logic
CREATE POLICY "Staff can view all reservations" ON public.reservations
    FOR SELECT USING (false);

CREATE POLICY "Staff can manage all reservations" ON public.reservations
    FOR ALL USING (false);

-- Payments policies
CREATE POLICY "Users can view their own payments" ON public.payments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.reservations 
            WHERE id = reservation_id AND guest_id = auth.uid()
        )
    );

-- Temporarily disable staff-only payment access to avoid recursion
-- This will be handled through application logic
CREATE POLICY "Staff can view all payments" ON public.payments
    FOR SELECT USING (false);

CREATE POLICY "System can manage payments" ON public.payments
    FOR ALL USING (true);

-- Create storage bucket for room images
INSERT INTO storage.buckets (id, name, public) VALUES ('room-images', 'room-images', true);

-- Storage policies
CREATE POLICY "Anyone can view room images" ON storage.objects
    FOR SELECT USING (bucket_id = 'room-images');

-- Temporarily disable admin-only storage access to avoid recursion
-- This will be handled through application logic
CREATE POLICY "Admins can upload room images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'room-images' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "Admins can update room images" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'room-images' AND
        auth.role() = 'authenticated'
    );

CREATE POLICY "Admins can delete room images" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'room-images' AND
        auth.role() = 'authenticated'
    );
