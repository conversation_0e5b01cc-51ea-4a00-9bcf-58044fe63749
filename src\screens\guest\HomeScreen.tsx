import React from 'react';
import ReactNative from 'react-native';
const {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  ImageBackground,
  Dimensions,
  StatusBar,
  Animated,
  TouchableOpacity,
} = ReactNative;
import {
  Text,
  Card,
  Button,
  Avatar,
  Chip,
  Divider,
  IconButton,
  Surface,
} from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { MaterialIcons, Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';

import { useAuthStore } from '../../store/authStore';
import { useRoomStore } from '../../store/roomStore';
import { useReservationStore } from '../../store/reservationStore';
import { Colors, Spacing, Typography, lightColors } from '../../constants';
import { getInitials } from '../../utils/stringUtils';
import RoomCard from '../../components/cards/RoomCard';
import type { GuestNavigationProp } from '../../navigation/GuestNavigator';
import type { Room, ReservationWithDetails } from '../../types/database';

type Reservation = ReservationWithDetails;

const { width } = Dimensions.get('window');

export const HomeScreen = () => {
  const navigation = useNavigation();
  const { user } = useAuthStore();
  const { rooms, fetchRooms, loading: roomsLoading } = useRoomStore();
  const { userReservations, fetchUserReservations, loading: reservationsLoading } = useReservationStore();
  const [refreshing, setRefreshing] = React.useState(false);
  const scrollY = React.useRef(new Animated.Value(0)).current;

  // Get featured rooms (limit to 3)
  const featuredRooms = rooms.filter((room: Room) => room.status === 'available').slice(0, 3);
  // Get upcoming reservations
  const upcomingReservations = userReservations
    .filter((reservation: Reservation) =>
      reservation.status === 'confirmed' &&
      new Date(reservation.check_in_date) >= new Date()
    )
    .slice(0, 2);
  React.useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    if (user) {
      await Promise.all([
        fetchRooms({ status: 'available' }),
        fetchUserReservations(user.id)
      ]);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'browse':
        navigation.navigate('Rooms');
        break;
      case 'reservations':
        navigation.navigate('Reservations');
        break;
      case 'profile':
        navigation.navigate('Profile');
        break;
    }
  };

  // Parallax effect for hero section
  const headerTranslateY = scrollY.interpolate({
    inputRange: [0, 200],
    outputRange: [0, -50],
    extrapolate: 'clamp',
  });

  const headerOpacity = scrollY.interpolate({
    inputRange: [0, 200],
    outputRange: [1, 0.8],
    extrapolate: 'clamp',
  });

  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      <Animated.ScrollView
        style={styles.container}
        showsVerticalScrollIndicator={false}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { y: scrollY } } }],
          { useNativeDriver: false }
        )}
        scrollEventThrottle={16}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#2E8B57']}
            tintColor={'#2E8B57'}
            progressViewOffset={100}
          />
        }
      >
        {/* Modern Hero Section */}
        <Animated.View
          style={[
            styles.heroContainer,
            {
              transform: [{ translateY: headerTranslateY }],
              opacity: headerOpacity,
            }
          ]}
        >
          <ImageBackground
            source={{
              uri: 'https://images.unsplash.com/photo-1571896349842-33c89424de2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1200&q=80'
            }}
            style={styles.heroSection}
            imageStyle={styles.heroImage}
          >
            <LinearGradient
              colors={['rgba(0,0,0,0.3)', 'rgba(0,0,0,0.7)']}
              style={styles.heroGradient}
            >
              <View style={styles.heroContent}>
                <View style={styles.welcomeContainer}>
                  <View style={styles.avatarContainer}>
                    <Avatar.Text
                      size={64}
                      label={getInitials(user?.full_name)}
                      style={styles.avatar}
                    />
                    <View style={styles.onlineIndicator} />
                  </View>
                  <View style={styles.welcomeText}>
                    <Text style={styles.greeting}>{getGreeting()},</Text>
                    <Text style={styles.userName}>{user?.full_name || 'Guest'}</Text>
                  </View>
                </View>

                <View style={styles.hotelBranding}>
                  <Text style={styles.hotelName}>Sunset View Hotel</Text>
                  <Text style={styles.tagline}>Experience Luxury & Comfort</Text>
                </View>
              </View>
            </LinearGradient>
          </ImageBackground>
        </Animated.View>

        {/* Modern Quick Actions */}
        <View style={styles.quickActionsContainer}>
          <BlurView intensity={20} style={styles.quickActionsBlur}>
            <View style={styles.quickActionsContent}>
              <Text style={styles.quickActionsTitle}>Quick Actions</Text>
              <View style={styles.quickActionsGrid}>
                <TouchableOpacity
                  style={styles.modernQuickAction}
                  onPress={() => handleQuickAction('browse')}
                  activeOpacity={0.8}
                >
                  <LinearGradient
                    colors={['#2E8B57', '#228B22']}
                    style={styles.quickActionGradient}
                  >
                    <Ionicons name="search" size={24} color="white" />
                  </LinearGradient>
                  <Text style={styles.quickActionLabel}>Browse Rooms</Text>
                  <Text style={styles.quickActionSubtext}>Find your perfect stay</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.modernQuickAction}
                  onPress={() => handleQuickAction('reservations')}
                  activeOpacity={0.8}
                >
                  <LinearGradient
                    colors={['#FFD700', '#DAA520']}
                    style={styles.quickActionGradient}
                  >
                    <Ionicons name="calendar" size={24} color="white" />
                  </LinearGradient>
                  <Text style={styles.quickActionLabel}>My Bookings</Text>
                  <Text style={styles.quickActionSubtext}>Manage reservations</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.modernQuickAction}
                  onPress={() => handleQuickAction('profile')}
                  activeOpacity={0.8}
                >
                  <LinearGradient
                    colors={['#3B82F6', '#60A5FA']}
                    style={styles.quickActionGradient}
                  >
                    <Ionicons name="person" size={24} color="white" />
                  </LinearGradient>
                  <Text style={styles.quickActionLabel}>Profile</Text>
                  <Text style={styles.quickActionSubtext}>Account settings</Text>
                </TouchableOpacity>
              </View>
            </View>
          </BlurView>
        </View>

      {/* Upcoming Reservations */}
      {upcomingReservations.length > 0 && (
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Upcoming Stays</Text>
            <Button
              mode="text"
              onPress={() => navigation.navigate('Reservations')}
            >
              View All
            </Button>
          </View>
          {upcomingReservations.map((reservation: Reservation) => (
            <Card key={reservation.id} style={styles.reservationCard}>
              <Card.Content>
                <View style={styles.reservationHeader}>
                  <Text style={styles.roomNumber}>
                    Room {reservation.room?.room_number || 'N/A'}
                  </Text>
                  <Chip
                    mode="outlined"
                    textStyle={styles.chipText}
                    style={[styles.chip, { borderColor: Colors.success }]}
                    compact={false}
                  >
                    {reservation.status}
                  </Chip>
                </View>
                <View style={styles.reservationDates}>
                  <View style={styles.dateInfo}>
                      <MaterialIcons name="login" size={16} color={Colors.textSecondary} />
                    <Text style={styles.dateText}>
                    Check-in: {new Date(reservation.check_in_date).toLocaleDateString()}
                  </Text>
                </View>
                <View style={styles.dateInfo}>
                  <MaterialIcons name="logout" size={16} color={Colors.textSecondary} />
                  <Text style={styles.dateText}>
                    Check-out: {new Date(reservation.check_out_date).toLocaleDateString()}
                  </Text>
                  </View>
                </View>
              </Card.Content>
            </Card>
          ))}
        </View>
      )}

      {/* Featured Rooms */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Featured Rooms</Text>
          <Button
            mode="text"
            onPress={() => navigation.navigate('Rooms')}
          >
            View All
          </Button>
        </View>

        {featuredRooms.map((room: Room) => (
          <React.Fragment key={room.id}>
            <RoomCard
              room={room}
              onPress={() => navigation.navigate('RoomDetails', { roomId: room.id })}
              style={styles.roomCard}
            />
          </React.Fragment>
        ))}
        {featuredRooms.length === 0 && !roomsLoading && (
          <Card style={styles.emptyCard}>
            <Card.Content style={styles.emptyContent}>
              <MaterialIcons name="hotel" size={48} color={Colors.textSecondary} />
              <Text style={styles.emptyText}>No rooms available at the moment</Text>
              <Button
                mode="outlined"
                onPress={() => fetchRooms()}
                style={styles.retryButton}
              >
                Refresh
              </Button>
            </Card.Content>
          </Card>
        )}
      </View>

        {/* Modern App Features */}
        <View style={styles.section}>
          <Text style={styles.modernSectionTitle}>Why Choose Us</Text>
          <View style={styles.modernFeaturesGrid}>
            <View style={styles.modernFeatureCard}>
              <LinearGradient
                colors={['#10B981', '#22C55E']}
                style={styles.featureIconContainer}
              >
                <Ionicons name="shield-checkmark" size={28} color="white" />
              </LinearGradient>
              <Text style={styles.modernFeatureTitle}>Secure Payments</Text>
              <Text style={styles.modernFeatureDesc}>Pay safely with Paystack & M-Pesa</Text>
            </View>

            <View style={styles.modernFeatureCard}>
              <LinearGradient
                colors={['#F59E0B', '#D97706']}
                style={styles.featureIconContainer}
              >
                <Ionicons name="notifications" size={28} color="white" />
              </LinearGradient>
              <Text style={styles.modernFeatureTitle}>Instant Updates</Text>
              <Text style={styles.modernFeatureDesc}>Real-time booking notifications</Text>
            </View>

            <View style={styles.modernFeatureCard}>
              <LinearGradient
                colors={['#3B82F6', '#60A5FA']}
                style={styles.featureIconContainer}
              >
                <Ionicons name="headset" size={28} color="white" />
              </LinearGradient>
              <Text style={styles.modernFeatureTitle}>24/7 Support</Text>
              <Text style={styles.modernFeatureDesc}>Always here to help you</Text>
            </View>

            <View style={styles.modernFeatureCard}>
              <LinearGradient
                colors={['#FFD700', '#DAA520']}
                style={styles.featureIconContainer}
              >
                <Ionicons name="star" size={28} color="white" />
              </LinearGradient>
              <Text style={styles.modernFeatureTitle}>Premium Service</Text>
              <Text style={styles.modernFeatureDesc}>Luxury hospitality experience</Text>
            </View>
          </View>
        </View>
      </Animated.ScrollView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  // Modern Hero Section
  heroContainer: {
    position: 'relative',
  },
  heroSection: {
    height: 320,
    justifyContent: 'center',
    alignItems: 'center',
  },
  heroImage: {
    resizeMode: 'cover',
  },
  heroGradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  heroContent: {
    paddingHorizontal: Spacing.large,
    alignItems: 'center',
  },
  welcomeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.large,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: Spacing.large,
  },
  avatar: {
    backgroundColor: '#2E8B57',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#10B981',
    borderWidth: 2,
    borderColor: 'white',
  },
  welcomeText: {
    alignItems: 'flex-start',
  },
  greeting: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: '400',
  },
  userName: {
    fontSize: 24,
    color: 'white',
    fontWeight: '700',
    marginTop: 2,
  },
  hotelBranding: {
    alignItems: 'center',
  },
  hotelName: {
    fontSize: 32,
    color: 'white',
    fontWeight: '800',
    textAlign: 'center',
    marginBottom: Spacing.small,
    letterSpacing: 0.5,
  },
  tagline: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    fontWeight: '300',
  },
  // Modern Quick Actions
  quickActionsContainer: {
    marginHorizontal: Spacing.medium,
    marginTop: -40,
    marginBottom: Spacing.large,
  },
  quickActionsBlur: {
    borderRadius: 20,
    overflow: 'hidden',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  quickActionsContent: {
    padding: Spacing.large,
  },
  quickActionsTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1A1A1A',
    marginBottom: Spacing.medium,
    textAlign: 'center',
  },
  quickActionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modernQuickAction: {
    alignItems: 'center',
    flex: 1,
    marginHorizontal: Spacing.small,
  },
  quickActionGradient: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.small,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  quickActionLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1A1A1A',
    textAlign: 'center',
    marginBottom: 2,
  },
  quickActionSubtext: {
    fontSize: 11,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 14,
  },
  section: {
    padding: Spacing.medium,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.medium,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: '#1A1A1A',
    marginBottom: Spacing.small,
  },
  modernSectionTitle: {
    fontSize: 24,
    fontWeight: '800',
    color: '#1A1A1A',
    marginBottom: Spacing.large,
    textAlign: 'center',
  },
  reservationCard: {
    marginBottom: Spacing.medium,
  },
  reservationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.small,
    minHeight: 40,
  },
  roomNumber: {
    ...Typography.h4,
    fontWeight: 'bold',
  },
  chip: {
    height: 32,
    minWidth: 80,
    paddingHorizontal: 8,
  },
  chipText: {
    fontSize: 12,
    fontWeight: '500',
  },
  reservationDates: {
    gap: Spacing.small,
  },
  dateInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.small,
  },
  dateText: {
    ...Typography.body,
    color: Colors.textSecondary,
  },
  roomCard: {
    marginBottom: Spacing.medium,
  },
  emptyCard: {
    marginVertical: Spacing.medium,
  },
  emptyContent: {
    alignItems: 'center',
    paddingVertical: Spacing.large,
  },
  emptyText: {
    ...Typography.body,
    color: Colors.textSecondary,
    marginVertical: Spacing.medium,
    textAlign: 'center',
  },
  retryButton: {
    marginTop: Spacing.small,
  },
  // Modern Features Grid
  modernFeaturesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: Spacing.medium,
  },
  modernFeatureCard: {
    width: (width - Spacing.medium * 3) / 2,
    backgroundColor: 'white',
    padding: Spacing.large,
    borderRadius: 16,
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    marginBottom: Spacing.medium,
  },
  featureIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.medium,
  },
  modernFeatureTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#1A1A1A',
    textAlign: 'center',
    marginBottom: Spacing.small,
  },
  modernFeatureDesc: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 16,
  },
});
