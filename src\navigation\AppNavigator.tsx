import React, { useEffect, useState, useRef } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useAuthStore } from '../store/authStore';
import { usePermissions } from '../hooks/usePermissions';

// Screen imports
import LoadingScreen from '../screens/LoadingScreen';
import AuthNavigator from './AuthNavigator';
import PublicNavigator from './PublicNavigator';
import GuestNavigator from './GuestNavigator';
import { AdminNavigator } from './AdminNavigator';

const Stack = createNativeStackNavigator();

// Main Tab Navigator for authenticated users
function AuthenticatedNavigator() {
  const { isStaff } = usePermissions();

  if (isStaff) {
    return <AdminNavigator />;
  }

  return <GuestNavigator />;
}

// Root Navigator with Browse-First Flow
export const AppNavigator = () => {
  const { user, session, loading, initialize, pendingBookingIntent, clearPendingBookingIntent } = useAuthStore();
  const [isInitializing, setIsInitializing] = useState(true);
  const navigationRef = useRef<any>(null);

  useEffect(() => {
    const initializeAuth = async () => {
      await initialize();
      setIsInitializing(false);
    };
    initializeAuth();
  }, []);

  // Handle pending booking intent after authentication
  useEffect(() => {
    if (user && session && pendingBookingIntent && navigationRef.current) {
      // Small delay to ensure navigation is ready
      const timer = setTimeout(() => {
        handlePendingBookingIntent();
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [user, session, pendingBookingIntent]);

  const handlePendingBookingIntent = () => {
    if (!pendingBookingIntent || !navigationRef.current) return;

    const { returnTo, roomId, room, checkIn, checkOut, guests } = pendingBookingIntent;

    try {
      if (returnTo === 'booking' && room) {
        // Navigate directly to booking screen with room and date parameters
        navigationRef.current.navigate('Authenticated', {
          screen: 'Rooms',
          params: {
            screen: 'Booking',
            params: {
              room,
              checkIn: checkIn || new Date().toISOString().split('T')[0],
              checkOut: checkOut || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0],
              guests: guests || 1,
            }
          }
        });
      } else if (roomId) {
        // Navigate to room details if we only have roomId
        navigationRef.current.navigate('Authenticated', {
          screen: 'Rooms',
          params: {
            screen: 'RoomDetails',
            params: {
              roomId,
              room,
            }
          }
        });
      } else {
        // Default to rooms tab
        navigationRef.current.navigate('Authenticated', {
          screen: 'Rooms'
        });
      }

      // Clear the pending intent after navigation
      clearPendingBookingIntent();
    } catch (error) {
      console.error('Error handling pending booking intent:', error);
      clearPendingBookingIntent();
    }
  };

  if (isInitializing || loading) {
    return <LoadingScreen />;
  }

  return (
    <NavigationContainer ref={navigationRef}>
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {user && session ? (
          // User is authenticated - show authenticated screens
          <Stack.Screen name="Authenticated" component={AuthenticatedNavigator} />
        ) : (
          // User is not authenticated - show public browsing
          <>
            <Stack.Screen name="Public" component={PublicNavigator} />
            {/* Auth screens as modal */}
            <Stack.Screen
              name="Auth"
              component={AuthNavigator}
              options={{
                presentation: 'modal',
                headerShown: false,
              }}
            />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};
