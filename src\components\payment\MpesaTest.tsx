import React, { useState } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { Button, Text, Card, Chip } from 'react-native-paper';
import { Paystack } from 'react-native-paystack-webview';
import { PAYSTACK_CONFIG } from '../../constants';
import { toCents } from '../../utils/currency';

export const MpesaTest = () => {
  const [showPaystack, setShowPaystack] = useState(false);
  const [testAmount] = useState(500); // KES 500 for M-Pesa testing

  const handleMpesaPayment = () => {
    console.log('🚀 Starting M-Pesa payment test...');
    console.log('💰 Amount:', testAmount, 'KES');
    console.log('🔑 Public Key:', PAYSTACK_CONFIG.publicKey);
    setShowPaystack(true);
  };

  const handleSuccess = (response: any) => {
    setShowPaystack(false);
    console.log('✅ M-Pesa Payment Success:', response);
    Alert.alert(
      '🎉 Payment Successful!', 
      `M-Pesa payment completed!\n\nReference: ${response.reference}\nAmount: KES ${testAmount}`,
      [{ text: 'OK', style: 'default' }]
    );
  };

  const handleCancel = () => {
    setShowPaystack(false);
    console.log('❌ M-Pesa Payment Cancelled');
    Alert.alert('Payment Cancelled', 'M-Pesa payment was cancelled by user');
  };

  return (
    <View style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.title}>🇰🇪 M-Pesa Payment Test</Text>
          <Text style={styles.subtitle}>Test Paystack M-Pesa Integration</Text>
          
          <View style={styles.infoContainer}>
            <Chip icon="cellphone" style={styles.chip}>M-Pesa Ready</Chip>
            <Chip icon="cash" style={styles.chip}>KES {testAmount}</Chip>
            <Chip icon="shield-check" style={styles.chip}>Test Mode</Chip>
          </View>

          <Text style={styles.description}>
            This will test M-Pesa payment through Paystack with mobile money prioritized.
            You should see M-Pesa as the first payment option.
          </Text>
          
          <Button
            mode="contained"
            onPress={handleMpesaPayment}
            style={styles.button}
            icon="cellphone"
          >
            Pay KES {testAmount} with M-Pesa
          </Button>
        </Card.Content>
      </Card>

      {/* M-Pesa Optimized Paystack Configuration */}
      {showPaystack && (
        <Paystack
          paystackKey={PAYSTACK_CONFIG.publicKey}
          amount={toCents(testAmount)}
          billingEmail="<EMAIL>"
          billingName="M-Pesa Test User"
          currency="KES"
          reference={`MPESA_TEST_${Date.now()}`}
          onCancel={handleCancel}
          onSuccess={handleSuccess}
          autoStart={false}
          billingMobile="************"
          // Prioritize mobile_money (M-Pesa) as first option
          channels={['mobile_money', 'card', 'bank', 'ussd']}
          metadata={{
            payment_type: 'mpesa_test',
            preferred_method: 'mobile_money',
            custom_fields: [
              {
                display_name: "Payment Method",
                variable_name: "payment_method",
                value: "M-Pesa"
              },
              {
                display_name: "Test Type",
                variable_name: "test_type", 
                value: "Mobile Money Priority"
              }
            ]
          }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
    backgroundColor: '#f5f5f5',
  },
  card: {
    padding: 20,
    elevation: 4,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#2E7D32',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666',
  },
  infoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
    flexWrap: 'wrap',
  },
  chip: {
    margin: 4,
  },
  description: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    color: '#555',
    lineHeight: 20,
  },
  button: {
    marginTop: 10,
    backgroundColor: '#2E7D32',
  },
});
