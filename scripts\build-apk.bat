@echo off
REM Sunset View Hotel - Local APK Build Script (Batch Version)
REM This is a simple wrapper for the PowerShell script

echo.
echo 🏨 Sunset View Hotel - Local APK Builder
echo ========================================
echo.

REM Check if PowerShell is available
powershell -Command "Write-Host 'PowerShell is available'" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ PowerShell is not available. Please install PowerShell.
    pause
    exit /b 1
)

REM Get the directory of this batch file
set SCRIPT_DIR=%~dp0

REM Check if PowerShell script exists
if not exist "%SCRIPT_DIR%setup-local-build.ps1" (
    echo ❌ PowerShell script not found: %SCRIPT_DIR%setup-local-build.ps1
    pause
    exit /b 1
)

echo Select build type:
echo 1. Debug Build (faster, for testing)
echo 2. Release Build (optimized, for distribution)
echo 3. Build Only (skip setup)
echo 4. Setup Only (no build)
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" (
    echo Running debug build...
    powershell -ExecutionPolicy Bypass -File "%SCRIPT_DIR%setup-local-build.ps1" -BuildType debug
) else if "%choice%"=="2" (
    echo Running release build...
    powershell -ExecutionPolicy Bypass -File "%SCRIPT_DIR%setup-local-build.ps1" -BuildType release
) else if "%choice%"=="3" (
    echo Running build only...
    powershell -ExecutionPolicy Bypass -File "%SCRIPT_DIR%setup-local-build.ps1" -BuildOnly -BuildType debug
) else if "%choice%"=="4" (
    echo Running setup only...
    powershell -ExecutionPolicy Bypass -File "%SCRIPT_DIR%setup-local-build.ps1" -SkipPrerequisites
) else (
    echo Invalid choice. Running debug build by default...
    powershell -ExecutionPolicy Bypass -File "%SCRIPT_DIR%setup-local-build.ps1" -BuildType debug
)

echo.
echo Build process completed. Check the output above for results.
pause
