# EAS Build ignore file
# Exclude problematic directories and files

# AI/Development tools and problematic directories
.trae/
**/.trae/

# OneDrive sync files and symbolic links
desktop.ini
*.lnk
**/*.lnk
**/.DS_Store

# Supabase SQL files (not needed for build)
supabase/*.sql
supabase/**/*.sql

# Documentation
docs/
**/*.md
README.md
CHANGELOG.md

# Test files and development scripts
src/tests/
**/*test*
test-signup.js
scripts/
**/*.test.js
**/*.test.ts
**/*.spec.js
**/*.spec.ts

# Development and temporary files
.vscode/
.idea/
*.tmp
*.temp
**/*.tmp
**/*.temp

# Build artifacts that might cause issues
.expo/
dist/
build/
**/.expo/

# Log files
*.log
**/*.log
logs/

# Cache directories
.cache/
**/.cache/
node_modules/.cache/

# OS specific files
Thumbs.db
.DS_Store
**/.DS_Store

# Development files
.env
.env.example

# Git
.git/

# Node modules (already excluded by default)
node_modules/

# Expo
.expo/

# Build artifacts
dist/
web-build/

# Debug files
npm-debug.*
yarn-debug.*
yarn-error.*

# OS files
.DS_Store
*.pem

# TypeScript
*.tsbuildinfo

# Temporary files
*.tmp
*.temp
