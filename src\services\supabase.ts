import { createClient } from '@supabase/supabase-js';
import { SUPABASE_CONFIG } from '../constants';
import type { Database } from '../types/database';

// Create Supabase client
export const supabase = createClient(
  SUPABASE_CONFIG.url,
  SUPABASE_CONFIG.anonKey,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: false,
    },
  }
);

// Auth service
export const authService = {
  // Sign up with email and password
  async signUp(email: string, password: string, userData: { full_name: string; phone?: string }) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: userData.full_name,
          phone: userData.phone,
          role: 'guest', // Default role
        },
      },
    });
    return { data, error };
  },

  // Sign in with email and password
  async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { data, error };
  },

  // Sign out
  async signOut() {
    const { error } = await supabase.auth.signOut();
    return { error };
  },

  // Reset password
  async resetPassword(email: string) {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email);
    return { data, error };
  },

  // Update password
  async updatePassword(password: string) {
    const { data, error } = await supabase.auth.updateUser({
      password,
    });
    return { data, error };
  },

  // Get current session
  async getSession() {
    const { data, error } = await supabase.auth.getSession();
    return { data, error };
  },

  // Get current user
  async getUser() {
    const { data, error } = await supabase.auth.getUser();
    return { data, error };
  },

  // Listen to auth changes
  onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(callback);
  },
};

// Helper function to normalize images format
const normalizeImages = (images: any): any[] => {
  if (!images) return [];

  // If it's already an array of objects (JSONB format), return as is
  if (Array.isArray(images) && images.length > 0 && typeof images[0] === 'object' && images[0].id) {
    return images;
  }

  // If it's an array of strings (TEXT[] format), convert to object format
  if (Array.isArray(images) && images.length > 0 && typeof images[0] === 'string') {
    return images.map((url: string, index: number) => ({
      id: `legacy_${Date.now()}_${index}`,
      url,
      alt_text: `Room image ${index + 1}`,
      upload_date: new Date().toISOString(),
      file_name: `legacy_image_${index + 1}.jpg`,
      file_size: null
    }));
  }

  return [];
};

// Room service
export const roomService = {
  // Get all rooms with optional filters
  async getRooms(filters?: {
    room_type?: string;
    status?: string;
    check_in_date?: string;
    check_out_date?: string;
  }) {
    let query = supabase
      .from('rooms')
      .select('*')
      .order('room_number');

    if (filters?.room_type) {
      query = query.eq('room_type', filters.room_type);
    }

    if (filters?.status) {
      query = query.eq('status', filters.status);
    }

    // If dates are provided, check availability
    if (filters?.check_in_date && filters?.check_out_date) {
      // This would require a more complex query to check for overlapping reservations
      // For now, we'll handle this in the application logic
    }

    const { data, error } = await query;

    // Transform the data to match frontend expectations
    const transformedData = data?.map(room => ({
      ...room,
      type: room.room_type, // Add type field for frontend compatibility
      images: normalizeImages(room.images), // Normalize images format
    }));

    return { data: transformedData, error };
  },

  // Get room by ID
  async getRoomById(id: string) {
    const { data, error } = await supabase
      .from('rooms')
      .select('*')
      .eq('id', id)
      .single();

    // Transform the data to match frontend expectations
    const transformedData = data ? {
      ...data,
      type: data.room_type, // Add type field for frontend compatibility
      images: normalizeImages(data.images), // Normalize images format
    } : null;

    return { data: transformedData, error };
  },

  // Create new room (admin only)
  async createRoom(roomData: any) {
    // Map the field names to match database schema
    const dbRoomData = {
      room_number: roomData.room_number,
      room_type: roomData.type || roomData.room_type, // Handle both field names
      bed_type: roomData.bed_type,
      size_sqm: roomData.size_sqm,
      price_per_night: roomData.price_per_night,
      description: roomData.description,
      amenities: roomData.amenities || [],
      max_occupancy: roomData.max_occupancy,
      images: roomData.images || [],
      status: roomData.status || 'available',
      is_available: roomData.is_available !== undefined ? roomData.is_available : true,
    };

    const { data, error } = await supabase
      .from('rooms')
      .insert([dbRoomData])
      .select()
      .single();

    // Transform the returned data to match frontend expectations
    const transformedData = data ? {
      ...data,
      type: data.room_type,
      images: normalizeImages(data.images),
    } : null;

    return { data: transformedData, error };
  },

  // Update room (admin only)
  async updateRoom(id: string, roomData: any) {
    // Map the field names to match database schema
    const dbRoomData: any = {};

    if (roomData.room_number !== undefined) dbRoomData.room_number = roomData.room_number;
    if (roomData.type !== undefined || roomData.room_type !== undefined) {
      dbRoomData.room_type = roomData.type || roomData.room_type;
    }
    if (roomData.bed_type !== undefined) dbRoomData.bed_type = roomData.bed_type;
    if (roomData.size_sqm !== undefined) dbRoomData.size_sqm = roomData.size_sqm;
    if (roomData.price_per_night !== undefined) dbRoomData.price_per_night = roomData.price_per_night;
    if (roomData.description !== undefined) dbRoomData.description = roomData.description;
    if (roomData.amenities !== undefined) dbRoomData.amenities = roomData.amenities;
    if (roomData.max_occupancy !== undefined) dbRoomData.max_occupancy = roomData.max_occupancy;
    if (roomData.images !== undefined) dbRoomData.images = roomData.images;
    if (roomData.status !== undefined) dbRoomData.status = roomData.status;
    if (roomData.is_available !== undefined) dbRoomData.is_available = roomData.is_available;

    const { data, error } = await supabase
      .from('rooms')
      .update(dbRoomData)
      .eq('id', id)
      .select()
      .single();

    // Transform the returned data to match frontend expectations
    const transformedData = data ? {
      ...data,
      type: data.room_type,
      images: normalizeImages(data.images),
    } : null;

    return { data: transformedData, error };
  },

  // Delete room (admin only)
  async deleteRoom(id: string) {
    const { data, error } = await supabase
      .from('rooms')
      .delete()
      .eq('id', id);
    return { data, error };
  },

  // Check room availability
  async checkAvailability(roomId: string, checkIn: string, checkOut: string) {
    const { data, error } = await supabase
      .from('reservations')
      .select('id')
      .eq('room_id', roomId)
      .eq('status', 'confirmed')
      .or(`check_in_date.lte.${checkOut},check_out_date.gte.${checkIn}`);
    
    return { 
      data: data ? data.length === 0 : false, // Available if no overlapping reservations
      error 
    };
  },
};

// Reservation service
export const reservationService = {
  // Get user's reservations
  async getUserReservations(userId: string) {
    const { data, error } = await supabase
      .from('reservations')
      .select(`
        *,
        room:rooms(*),
        payment:payments(*)
      `)
      .eq('guest_id', userId)
      .order('created_at', { ascending: false });
    return { data, error };
  },

  // Get all reservations (admin only)
  async getAllReservations() {
    const { data, error } = await supabase
      .from('reservations')
      .select(`
        *,
        guest:users!guest_id(*),
        room:rooms(*),
        payment:payments(*)
      `)
      .order('check_in_date', { ascending: true });
    return { data, error };
  },

  // Get reservation by ID
  async getReservationById(id: string) {
    const { data, error } = await supabase
      .from('reservations')
      .select(`
        *,
        room:rooms(*),
        payment:payments(*)
      `)
      .eq('id', id)
      .single();
    return { data, error };
  },

  // Create new reservation
  async createReservation(reservationData: any) {
    const { data, error } = await supabase
      .from('reservations')
      .insert([reservationData])
      .select()
      .single();
    return { data, error };
  },

  // Update reservation
  async updateReservation(id: string, reservationData: any) {
    const { data, error } = await supabase
      .from('reservations')
      .update(reservationData)
      .eq('id', id)
      .select()
      .single();
    return { data, error };
  },

  // Cancel reservation
  async cancelReservation(id: string) {
    const { data, error } = await supabase
      .from('reservations')
      .update({ status: 'cancelled' })
      .eq('id', id)
      .select()
      .single();
    return { data, error };
  },
};

// Payment service
export const paymentService = {
  // Create payment record
  async createPayment(paymentData: any) {
    const { data, error } = await supabase
      .from('payments')
      .insert([paymentData])
      .select()
      .single();
    return { data, error };
  },

  // Update payment status
  async updatePaymentStatus(id: string, status: string, paystackData?: any) {
    const updateData: any = { status };
    
    if (paystackData) {
      updateData.paystack_reference = paystackData.reference;
      updateData.paystack_transaction_id = paystackData.transaction_id;
      if (status === 'paid') {
        updateData.paid_at = new Date().toISOString();
      }
    }

    const { data, error } = await supabase
      .from('payments')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();
    return { data, error };
  },

  // Update reservation
  async updateReservation(id: string, reservationData: any) {
    const { data, error } = await supabase
      .from('reservations')
      .update(reservationData)
      .eq('id', id)
      .select()
      .single();
    return { data, error };
  },

  // Get payment by reservation ID
  async getPaymentByReservationId(reservationId: string) {
    const { data, error } = await supabase
      .from('payments')
      .select('*')
      .eq('reservation_id', reservationId)
      .single();
    return { data, error };
  },
};

// Storage service for images
export const storageService = {
  // Upload room image
  async uploadRoomImage(file: File, roomId: string) {
    const fileExt = file.name.split('.').pop();
    const fileName = `${roomId}/${Date.now()}.${fileExt}`;
    
    const { data, error } = await supabase.storage
      .from('room-images')
      .upload(fileName, file);
    
    if (error) return { data: null, error };
    
    // Get public URL
    const { data: urlData } = supabase.storage
      .from('room-images')
      .getPublicUrl(fileName);
    
    return { data: urlData.publicUrl, error: null };
  },

  // Delete room image
  async deleteRoomImage(imagePath: string) {
    const { data, error } = await supabase.storage
      .from('room-images')
      .remove([imagePath]);
    return { data, error };
  },
};

export default supabase;
